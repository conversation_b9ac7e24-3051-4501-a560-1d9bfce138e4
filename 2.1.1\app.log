2025-05-30 21:06:16,837 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-30 21:06:16,839 - DEBUG - 检测到输入语言 (ko) 与当前模式定义的目标语言 (ko) 一致，触发反向翻译流程。
2025-05-30 21:06:16,839 - DEBUG - 检测到语言: ko, 输入语言: 韩文(ko), 输出语言: 中文(zh)
2025-05-30 21:06:16,840 - DEBUG - 使用语气词 - 输入: [ㅋ|ㅎ|아|네|헤|ㅜ], 输出: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜]
2025-05-30 21:06:16,840 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-30 21:06:16,840 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-30 21:06:16,840 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: 고양이나 강아지는 안 키우세요?
翻译: 不养猫或狗吗？
原文: 我有猫咪哦~妈妈送的。
翻译: 고양이를 키우고 있어요~ 엄마가 선물해주셨어요.
原文: 유튜브 어디서 검색해요
翻译: 在YouTube哪里搜索呢
原文: SM是什么？
翻译: SM이 무엇인가요?
原文: 女S男M。
翻译: 여성 S 남성 M.
原文: SM是什么意思？
翻译: SM이 무슨 뜻인가요?
原文: 무섭지 않으세요?ㅎ
翻译: 不害怕吗？嘿嘿
原文: 哈哈~骑太快我肯定会害怕。
翻译: ㅋ~ 너무 빨리 타면 분명 무서울 거예요.

将以下内容从韩文翻译成中文：
하하! 감사합니다! 한번 조사는 해봤었습니다. 제가 아는 동생도 타투리스트였거든요. 그런데 그친구도 안좋은 일을 조금 겪고 힘들어 하더군요...
2025-05-30 21:06:16,840 - DEBUG - 【构建提示词】长度: 963 字符
2025-05-30 21:06:17,069 - DEBUG - API密钥解密成功
2025-05-30 21:06:17,070 - DEBUG - API密钥解密成功
2025-05-30 21:06:17,070 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-30 21:06:17,070 - DEBUG - 思考模式已禁用（未设置thinkingConfig参数 或预算为0）
2025-05-30 21:06:17,071 - DEBUG - 明确禁用思考模式(generationConfig.thinkingConfig.thinkingBudget=0)
2025-05-30 21:06:18,229 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "哈哈！谢谢！我之前调查过一次。我认识的一个弟弟也是纹身师。但是那个朋友也遇到了一些不好的事情，过得很辛苦呢……"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 617,
    "candidatesTokenCount": 34,
    "totalTokenCount": 651,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 617
      }
    ]
  },
  "modelVersion": "models/gemini-2.5-flash-preview-04-17",
  "responseId": "Op85aMnfB_PQ1MkP2dDlYA"
}

2025-05-30 21:06:18,230 - DEBUG - 【API响应JSON对象】模型: gemini-2.5-flash-preview-04-17, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '哈哈！谢谢！我之前调查过一次。我认识的一个弟弟也是纹身师。但是那个朋友也遇到了一些不好的事情，过得很辛苦呢……'}], 'role': 'model'}, 'finishReason': 'STOP', 'index': 0}], 'usageMetadata': {'promptTokenCount': 617, 'candidatesTokenCount': 34, 'totalTokenCount': 651, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 617}]}, 'modelVersion': 'models/gemini-2.5-flash-preview-04-17', 'responseId': 'Op85aMnfB_PQ1MkP2dDlYA'}
2025-05-30 21:06:18,230 - DEBUG - 【Token使用情况】模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:06:18,230 - DEBUG -   - 思考Token数: 0
2025-05-30 21:06:18,230 - DEBUG -   - 提示Token数: 617
2025-05-30 21:06:18,231 - DEBUG -   - 输出Token数: 34
2025-05-30 21:06:18,231 - DEBUG -   - 总Token数: 651
2025-05-30 21:06:18,231 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 99, 1890.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:06:18,232 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.955)]
2025-05-30 21:06:18,232 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-30 21:06:18,232 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-30 21:06:18,233 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-30 21:06:18,233 - INFO - API翻译成功。模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:06:18,770 - DEBUG - 输入框内容已替换为: 哈哈！谢谢！我之前调查过一次。我认识的一个弟弟也是纹身师。但是那个朋友也遇到了一些不好的事情，过得很辛苦呢……
2025-05-30 21:06:18,771 - INFO - 【翻译结果】
哈哈！谢谢！我之前调查过一次。我认识的一个弟弟也是纹身师。但是那个朋友也遇到了一些不好的事情，过得很辛苦呢……
2025-05-30 21:06:18,771 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-30 21:06:18,772 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-30 21:06:18,773 - INFO - 翻译完成，结果已替换输入框内容
2025-05-30 21:06:18,775 - INFO - 已立即保存 2 条缓存记录
2025-05-30 21:06:18,778 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-30 21:06:42,521 - INFO - 检测到三次空格，触发翻译
2025-05-30 21:06:42,522 - INFO - 【原文】
你可以抽时间去学习，然后自己开工作室，不要给别人打工才是正确的。
2025-05-30 21:06:42,523 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 98, 2070.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:06:42,523 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.958)]
2025-05-30 21:06:42,523 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-30 21:06:42,523 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-30 21:06:42,523 - INFO - 检测到原文语言: zh
2025-05-30 21:06:42,524 - INFO - 执行正向翻译为: ko
2025-05-30 21:06:42,524 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-30 21:06:42,524 - DEBUG - 检测到语言: zh, 输入语言: 中文(zh), 输出语言: 韩文(ko)
2025-05-30 21:06:42,524 - DEBUG - 使用语气词 - 输入: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜], 输出: [ㅋ|ㅎ|아|네|헤|ㅜ]
2025-05-30 21:06:42,524 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-30 21:06:42,524 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-30 21:06:42,524 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 中文 和输出为 韩文 ，将内容从 中文 翻译成 韩文 ，使用敬语
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 韩文，调整语气和风格，考虑文化内涵和地区差异。不得包含 中文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 中的语气助词时，才将其翻译为 韩文 中等效的 [ㅋ|ㅎ|아|네|헤|ㅜ] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 韩文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 韩文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: 我有猫咪哦~妈妈送的。
翻译: 고양이를 키우고 있어요~ 엄마가 선물해주셨어요.
原文: 유튜브 어디서 검색해요
翻译: 在YouTube哪里搜索呢
原文: SM是什么？
翻译: SM이 무엇인가요?
原文: 女S男M。
翻译: 여성 S 남성 M.
原文: SM是什么意思？
翻译: SM이 무슨 뜻인가요?
原文: 무섭지 않으세요?ㅎ
翻译: 不害怕吗？嘿嘿
原文: 哈哈~骑太快我肯定会害怕。
翻译: ㅋ~ 너무 빨리 타면 분명 무서울 거예요.
原文: 하하! 감사합니다! 한번 조사는 해봤었습니다. 제가 아는 동생도 타투리스트였거든요. 그런데 그친구도 안좋은 일을 조금 겪고 힘들어 하더군요...
翻译: 哈哈！谢谢！我之前调查过一次。我认识的一个弟弟也是纹身师。但是那个朋友也遇到了一些不好的事情，过得很辛苦呢……

将以下内容从中文翻译成韩文，使用敬语：
你可以抽时间去学习，然后自己开工作室，不要给别人打工才是正确的。
2025-05-30 21:06:42,525 - DEBUG - 【构建提示词】长度: 1036 字符
2025-05-30 21:06:42,639 - DEBUG - API密钥解密成功
2025-05-30 21:06:42,639 - DEBUG - API密钥解密成功
2025-05-30 21:06:42,640 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-30 21:06:42,640 - DEBUG - 思考模式已禁用（未设置thinkingConfig参数 或预算为0）
2025-05-30 21:06:42,640 - DEBUG - 明确禁用思考模式(generationConfig.thinkingConfig.thinkingBudget=0)
2025-05-30 21:06:43,715 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "시간 내서 배우고 직접 스튜디오를 여는 게 맞아요. 남 밑에서 일하지 마세요."
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 660,
    "candidatesTokenCount": 23,
    "totalTokenCount": 683,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 660
      }
    ]
  },
  "modelVersion": "models/gemini-2.5-flash-preview-04-17",
  "responseId": "U585aPTgJf2d1MkPtN6EwQQ"
}

2025-05-30 21:06:43,715 - DEBUG - 【API响应JSON对象】模型: gemini-2.5-flash-preview-04-17, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '시간 내서 배우고 직접 스튜디오를 여는 게 맞아요. 남 밑에서 일하지 마세요.'}], 'role': 'model'}, 'finishReason': 'STOP', 'index': 0}], 'usageMetadata': {'promptTokenCount': 660, 'candidatesTokenCount': 23, 'totalTokenCount': 683, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 660}]}, 'modelVersion': 'models/gemini-2.5-flash-preview-04-17', 'responseId': 'U585aPTgJf2d1MkPtN6EwQQ'}
2025-05-30 21:06:43,716 - DEBUG - 【Token使用情况】模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:06:43,716 - DEBUG -   - 思考Token数: 0
2025-05-30 21:06:43,716 - DEBUG -   - 提示Token数: 660
2025-05-30 21:06:43,716 - DEBUG -   - 输出Token数: 23
2025-05-30 21:06:43,716 - DEBUG -   - 总Token数: 683
2025-05-30 21:06:43,717 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 99, 3614.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:06:43,717 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.902)]
2025-05-30 21:06:43,718 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-30 21:06:43,718 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-30 21:06:43,718 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-30 21:06:43,718 - INFO - API翻译成功。模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:06:44,257 - DEBUG - 输入框内容已替换为: 시간 내서 배우고 직접 스튜디오를 여는 게 맞아요. 남 밑에서 일하지 마세요.
2025-05-30 21:06:44,258 - INFO - 【翻译结果】
시간 내서 배우고 직접 스튜디오를 여는 게 맞아요. 남 밑에서 일하지 마세요.
2025-05-30 21:06:44,258 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-30 21:06:44,259 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-30 21:06:44,260 - INFO - 翻译完成，结果已替换输入框内容
2025-05-30 21:06:44,261 - INFO - 已立即保存 2 条缓存记录
2025-05-30 21:06:44,265 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-30 21:06:47,625 - INFO - 检测到三次空格，触发翻译
2025-05-30 21:06:47,626 - INFO - 【原文】
대만에선 몇년 지내셨어요?
2025-05-30 21:06:47,627 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 97, 3754.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:06:47,627 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.915)]
2025-05-30 21:06:47,627 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-30 21:06:47,628 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-30 21:06:47,628 - INFO - 检测到原文语言: ko
2025-05-30 21:06:47,628 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-30 21:06:47,628 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-30 21:06:47,628 - DEBUG - 检测到输入语言 (ko) 与当前模式定义的目标语言 (ko) 一致，触发反向翻译流程。
2025-05-30 21:06:47,628 - DEBUG - 检测到语言: ko, 输入语言: 韩文(ko), 输出语言: 中文(zh)
2025-05-30 21:06:47,628 - DEBUG - 使用语气词 - 输入: [ㅋ|ㅎ|아|네|헤|ㅜ], 输出: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜]
2025-05-30 21:06:47,628 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-30 21:06:47,628 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-30 21:06:47,629 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: 유튜브 어디서 검색해요
翻译: 在YouTube哪里搜索呢
原文: SM是什么？
翻译: SM이 무엇인가요?
原文: 女S男M。
翻译: 여성 S 남성 M.
原文: SM是什么意思？
翻译: SM이 무슨 뜻인가요?
原文: 무섭지 않으세요?ㅎ
翻译: 不害怕吗？嘿嘿
原文: 哈哈~骑太快我肯定会害怕。
翻译: ㅋ~ 너무 빨리 타면 분명 무서울 거예요.
原文: 하하! 감사합니다! 한번 조사는 해봤었습니다. 제가 아는 동생도 타투리스트였거든요. 그런데 그친구도 안좋은 일을 조금 겪고 힘들어 하더군요...
翻译: 哈哈！谢谢！我之前调查过一次。我认识的一个弟弟也是纹身师。但是那个朋友也遇到了一些不好的事情，过得很辛苦呢……
原文: 你可以抽时间去学习，然后自己开工作室，不要给别人打工才是正确的。
翻译: 시간 내서 배우고 직접 스튜디오를 여는 게 맞아요. 남 밑에서 일하지 마세요.

将以下内容从韩文翻译成中文：
대만에선 몇년 지내셨어요?
2025-05-30 21:06:47,629 - DEBUG - 【构建提示词】长度: 1046 字符
2025-05-30 21:06:47,828 - DEBUG - API密钥解密成功
2025-05-30 21:06:47,828 - DEBUG - API密钥解密成功
2025-05-30 21:06:47,828 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-30 21:06:47,828 - DEBUG - 思考模式已禁用（未设置thinkingConfig参数 或预算为0）
2025-05-30 21:06:47,829 - DEBUG - 明确禁用思考模式(generationConfig.thinkingConfig.thinkingBudget=0)
2025-05-30 21:06:48,756 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "在台湾住了几年？"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 662,
    "candidatesTokenCount": 5,
    "totalTokenCount": 667,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 662
      }
    ]
  },
  "modelVersion": "models/gemini-2.5-flash-preview-04-17",
  "responseId": "WJ85aJTIJ4eX1MkPou6nQQ"
}

2025-05-30 21:06:48,757 - DEBUG - 【API响应JSON对象】模型: gemini-2.5-flash-preview-04-17, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '在台湾住了几年？'}], 'role': 'model'}, 'finishReason': 'STOP', 'index': 0}], 'usageMetadata': {'promptTokenCount': 662, 'candidatesTokenCount': 5, 'totalTokenCount': 667, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 662}]}, 'modelVersion': 'models/gemini-2.5-flash-preview-04-17', 'responseId': 'WJ85aJTIJ4eX1MkPou6nQQ'}
2025-05-30 21:06:48,757 - DEBUG - 【Token使用情况】模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:06:48,758 - DEBUG -   - 思考Token数: 0
2025-05-30 21:06:48,758 - DEBUG -   - 提示Token数: 662
2025-05-30 21:06:48,758 - DEBUG -   - 输出Token数: 5
2025-05-30 21:06:48,758 - DEBUG -   - 总Token数: 667
2025-05-30 21:06:48,759 - DEBUG - pycld2 检测结果: is_reliable=False, details=(('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:06:48,760 - DEBUG - 基于特征补充候选: zh (score: 0.4375)
2025-05-30 21:06:48,760 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.438)]
2025-05-30 21:06:48,760 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-30 21:06:48,760 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-30 21:06:48,761 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-30 21:06:48,761 - INFO - API翻译成功。模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:06:49,290 - DEBUG - 输入框内容已替换为: 在台湾住了几年？
2025-05-30 21:06:49,291 - INFO - 【翻译结果】
在台湾住了几年？
2025-05-30 21:06:49,291 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-30 21:06:49,292 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-30 21:06:49,292 - INFO - 翻译完成，结果已替换输入框内容
2025-05-30 21:06:49,293 - INFO - 已立即保存 2 条缓存记录
2025-05-30 21:06:49,297 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-30 21:06:59,530 - INFO - 检测到三次空格，触发翻译
2025-05-30 21:06:59,531 - INFO - 【原文】
从小在台湾长大。经常来韩国探亲。
2025-05-30 21:06:59,531 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 97, 2048.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:06:59,532 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.942)]
2025-05-30 21:06:59,532 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-30 21:06:59,532 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-30 21:06:59,532 - INFO - 检测到原文语言: zh
2025-05-30 21:06:59,533 - INFO - 执行正向翻译为: ko
2025-05-30 21:06:59,533 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-30 21:06:59,533 - DEBUG - 检测到语言: zh, 输入语言: 中文(zh), 输出语言: 韩文(ko)
2025-05-30 21:06:59,533 - DEBUG - 使用语气词 - 输入: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜], 输出: [ㅋ|ㅎ|아|네|헤|ㅜ]
2025-05-30 21:06:59,533 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-30 21:06:59,533 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-30 21:06:59,534 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 中文 和输出为 韩文 ，将内容从 中文 翻译成 韩文 ，使用敬语
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 韩文，调整语气和风格，考虑文化内涵和地区差异。不得包含 中文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 中的语气助词时，才将其翻译为 韩文 中等效的 [ㅋ|ㅎ|아|네|헤|ㅜ] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 韩文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 韩文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: SM是什么？
翻译: SM이 무엇인가요?
原文: 女S男M。
翻译: 여성 S 남성 M.
原文: SM是什么意思？
翻译: SM이 무슨 뜻인가요?
原文: 무섭지 않으세요?ㅎ
翻译: 不害怕吗？嘿嘿
原文: 哈哈~骑太快我肯定会害怕。
翻译: ㅋ~ 너무 빨리 타면 분명 무서울 거예요.
原文: 하하! 감사합니다! 한번 조사는 해봤었습니다. 제가 아는 동생도 타투리스트였거든요. 그런데 그친구도 안좋은 일을 조금 겪고 힘들어 하더군요...
翻译: 哈哈！谢谢！我之前调查过一次。我认识的一个弟弟也是纹身师。但是那个朋友也遇到了一些不好的事情，过得很辛苦呢……
原文: 你可以抽时间去学习，然后自己开工作室，不要给别人打工才是正确的。
翻译: 시간 내서 배우고 직접 스튜디오를 여는 게 맞아요. 남 밑에서 일하지 마세요.
原文: 대만에선 몇년 지내셨어요?
翻译: 在台湾住了几年？

将以下内容从中文翻译成韩文，使用敬语：
从小在台湾长大。经常来韩国探亲。
2025-05-30 21:06:59,534 - DEBUG - 【构建提示词】长度: 1055 字符
2025-05-30 21:06:59,644 - DEBUG - API密钥解密成功
2025-05-30 21:06:59,644 - DEBUG - API密钥解密成功
2025-05-30 21:06:59,645 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-30 21:06:59,645 - DEBUG - 思考模式已禁用（未设置thinkingConfig参数 或预算为0）
2025-05-30 21:06:59,645 - DEBUG - 明确禁用思考模式(generationConfig.thinkingConfig.thinkingBudget=0)
2025-05-30 21:07:00,737 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "어릴 때부터 대만에서 자랐습니다. 한국에는 친척 방문으로 자주 왔습니다."
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 675,
    "candidatesTokenCount": 21,
    "totalTokenCount": 696,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 675
      }
    ]
  },
  "modelVersion": "models/gemini-2.5-flash-preview-04-17",
  "responseId": "ZJ85aLWhJ7av1MkP-OL52AM"
}

2025-05-30 21:07:00,738 - DEBUG - 【API响应JSON对象】模型: gemini-2.5-flash-preview-04-17, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '어릴 때부터 대만에서 자랐습니다. 한국에는 친척 방문으로 자주 왔습니다.'}], 'role': 'model'}, 'finishReason': 'STOP', 'index': 0}], 'usageMetadata': {'promptTokenCount': 675, 'candidatesTokenCount': 21, 'totalTokenCount': 696, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 675}]}, 'modelVersion': 'models/gemini-2.5-flash-preview-04-17', 'responseId': 'ZJ85aLWhJ7av1MkP-OL52AM'}
2025-05-30 21:07:00,738 - DEBUG - 【Token使用情况】模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:07:00,738 - DEBUG -   - 思考Token数: 0
2025-05-30 21:07:00,739 - DEBUG -   - 提示Token数: 675
2025-05-30 21:07:00,739 - DEBUG -   - 输出Token数: 21
2025-05-30 21:07:00,739 - DEBUG -   - 总Token数: 696
2025-05-30 21:07:00,740 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 99, 3723.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:07:00,740 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.918)]
2025-05-30 21:07:00,740 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-30 21:07:00,741 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-30 21:07:00,741 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-30 21:07:00,741 - INFO - API翻译成功。模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:07:01,256 - DEBUG - 输入框内容已替换为: 어릴 때부터 대만에서 자랐습니다. 한국에는 친척 방문으로 자주 왔습니다.
2025-05-30 21:07:01,257 - INFO - 【翻译结果】
어릴 때부터 대만에서 자랐습니다. 한국에는 친척 방문으로 자주 왔습니다.
2025-05-30 21:07:01,257 - DEBUG - 缓存已满，移除最久未使用项: 2-ko-zh--29405613273...
2025-05-30 21:07:01,257 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-30 21:07:01,258 - DEBUG - 缓存已满，移除最久未使用项: 2-zh-ko--49895760165...
2025-05-30 21:07:01,258 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-30 21:07:01,259 - INFO - 翻译完成，结果已替换输入框内容
2025-05-30 21:07:01,261 - INFO - 已立即保存 2 条缓存记录
2025-05-30 21:07:01,264 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-30 21:07:14,314 - INFO - 检测到三次空格，触发翻译
2025-05-30 21:07:14,315 - INFO - 【原文】
你现在在做什么呢？
2025-05-30 21:07:14,316 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 96, 1925.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:07:14,317 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.917)]
2025-05-30 21:07:14,317 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-30 21:07:14,318 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-30 21:07:14,318 - INFO - 检测到原文语言: zh
2025-05-30 21:07:14,318 - INFO - 执行正向翻译为: ko
2025-05-30 21:07:14,318 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-30 21:07:14,318 - DEBUG - 检测到语言: zh, 输入语言: 中文(zh), 输出语言: 韩文(ko)
2025-05-30 21:07:14,319 - DEBUG - 使用语气词 - 输入: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜], 输出: [ㅋ|ㅎ|아|네|헤|ㅜ]
2025-05-30 21:07:14,319 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-30 21:07:14,319 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-30 21:07:14,319 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 中文 和输出为 韩文 ，将内容从 中文 翻译成 韩文 ，使用敬语
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 韩文，调整语气和风格，考虑文化内涵和地区差异。不得包含 中文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 中的语气助词时，才将其翻译为 韩文 中等效的 [ㅋ|ㅎ|아|네|헤|ㅜ] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 韩文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 韩文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: 女S男M。
翻译: 여성 S 남성 M.
原文: SM是什么意思？
翻译: SM이 무슨 뜻인가요?
原文: 무섭지 않으세요?ㅎ
翻译: 不害怕吗？嘿嘿
原文: 哈哈~骑太快我肯定会害怕。
翻译: ㅋ~ 너무 빨리 타면 분명 무서울 거예요.
原文: 하하! 감사합니다! 한번 조사는 해봤었습니다. 제가 아는 동생도 타투리스트였거든요. 그런데 그친구도 안좋은 일을 조금 겪고 힘들어 하더군요...
翻译: 哈哈！谢谢！我之前调查过一次。我认识的一个弟弟也是纹身师。但是那个朋友也遇到了一些不好的事情，过得很辛苦呢……
原文: 你可以抽时间去学习，然后自己开工作室，不要给别人打工才是正确的。
翻译: 시간 내서 배우고 직접 스튜디오를 여는 게 맞아요. 남 밑에서 일하지 마세요.
原文: 대만에선 몇년 지내셨어요?
翻译: 在台湾住了几年？
原文: 从小在台湾长大。经常来韩国探亲。
翻译: 어릴 때부터 대만에서 자랐습니다. 한국에는 친척 방문으로 자주 왔습니다.

将以下内容从中文翻译成韩文，使用敬语：
你现在在做什么呢？
2025-05-30 21:07:14,319 - DEBUG - 【构建提示词】长度: 1088 字符
2025-05-30 21:07:14,428 - DEBUG - API密钥解密成功
2025-05-30 21:07:14,428 - DEBUG - API密钥解密成功
2025-05-30 21:07:14,429 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-30 21:07:14,429 - DEBUG - 思考模式已禁用（未设置thinkingConfig参数 或预算为0）
2025-05-30 21:07:14,430 - DEBUG - 明确禁用思考模式(generationConfig.thinkingConfig.thinkingBudget=0)
2025-05-30 21:07:15,317 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "지금 뭐 하고 계세요?"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 694,
    "candidatesTokenCount": 6,
    "totalTokenCount": 700,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 694
      }
    ]
  },
  "modelVersion": "models/gemini-2.5-flash-preview-04-17",
  "responseId": "c585aMeSDo2w1MkPsMe-0Qc"
}

2025-05-30 21:07:15,317 - DEBUG - 【API响应JSON对象】模型: gemini-2.5-flash-preview-04-17, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '지금 뭐 하고 계세요?'}], 'role': 'model'}, 'finishReason': 'STOP', 'index': 0}], 'usageMetadata': {'promptTokenCount': 694, 'candidatesTokenCount': 6, 'totalTokenCount': 700, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 694}]}, 'modelVersion': 'models/gemini-2.5-flash-preview-04-17', 'responseId': 'c585aMeSDo2w1MkPsMe-0Qc'}
2025-05-30 21:07:15,318 - DEBUG - 【Token使用情况】模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:07:15,318 - DEBUG -   - 思考Token数: 0
2025-05-30 21:07:15,318 - DEBUG -   - 提示Token数: 694
2025-05-30 21:07:15,319 - DEBUG -   - 输出Token数: 6
2025-05-30 21:07:15,319 - DEBUG -   - 总Token数: 700
2025-05-30 21:07:15,320 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 96, 3510.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:07:15,321 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.872)]
2025-05-30 21:07:15,321 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-30 21:07:15,321 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-30 21:07:15,321 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-30 21:07:15,321 - INFO - API翻译成功。模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:07:15,831 - DEBUG - 输入框内容已替换为: 지금 뭐 하고 계세요?
2025-05-30 21:07:15,832 - INFO - 【翻译结果】
지금 뭐 하고 계세요?
2025-05-30 21:07:15,832 - DEBUG - 缓存已满，移除最久未使用项: 2-zh-ko-582867810811...
2025-05-30 21:07:15,833 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-30 21:07:15,833 - DEBUG - 缓存已满，移除最久未使用项: 2-ko-zh-738539260311...
2025-05-30 21:07:15,834 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-30 21:07:15,834 - INFO - 翻译完成，结果已替换输入框内容
2025-05-30 21:07:15,836 - INFO - 已立即保存 2 条缓存记录
2025-05-30 21:07:15,839 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-30 21:07:29,842 - INFO - 检测到三次空格，触发翻译
2025-05-30 21:07:29,843 - INFO - 【原文】
이상한 용어들이 나오는데요
2025-05-30 21:07:29,844 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 97, 3780.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:07:29,844 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.936)]
2025-05-30 21:07:29,845 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-30 21:07:29,845 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-30 21:07:29,845 - INFO - 检测到原文语言: ko
2025-05-30 21:07:29,846 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-30 21:07:29,846 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-30 21:07:29,846 - DEBUG - 检测到输入语言 (ko) 与当前模式定义的目标语言 (ko) 一致，触发反向翻译流程。
2025-05-30 21:07:29,847 - DEBUG - 检测到语言: ko, 输入语言: 韩文(ko), 输出语言: 中文(zh)
2025-05-30 21:07:29,847 - DEBUG - 使用语气词 - 输入: [ㅋ|ㅎ|아|네|헤|ㅜ], 输出: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜]
2025-05-30 21:07:29,847 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-30 21:07:29,847 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-30 21:07:29,847 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: SM是什么意思？
翻译: SM이 무슨 뜻인가요?
原文: 무섭지 않으세요?ㅎ
翻译: 不害怕吗？嘿嘿
原文: 哈哈~骑太快我肯定会害怕。
翻译: ㅋ~ 너무 빨리 타면 분명 무서울 거예요.
原文: 하하! 감사합니다! 한번 조사는 해봤었습니다. 제가 아는 동생도 타투리스트였거든요. 그런데 그친구도 안좋은 일을 조금 겪고 힘들어 하더군요...
翻译: 哈哈！谢谢！我之前调查过一次。我认识的一个弟弟也是纹身师。但是那个朋友也遇到了一些不好的事情，过得很辛苦呢……
原文: 你可以抽时间去学习，然后自己开工作室，不要给别人打工才是正确的。
翻译: 시간 내서 배우고 직접 스튜디오를 여는 게 맞아요. 남 밑에서 일하지 마세요.
原文: 대만에선 몇년 지내셨어요?
翻译: 在台湾住了几年？
原文: 从小在台湾长大。经常来韩国探亲。
翻译: 어릴 때부터 대만에서 자랐습니다. 한국에는 친척 방문으로 자주 왔습니다.
原文: 你现在在做什么呢？
翻译: 지금 뭐 하고 계세요?

将以下内容从韩文翻译成中文：
이상한 용어들이 나오는데요
2025-05-30 21:07:29,847 - DEBUG - 【构建提示词】长度: 1089 字符
2025-05-30 21:07:30,080 - DEBUG - API密钥解密成功
2025-05-30 21:07:30,080 - DEBUG - API密钥解密成功
2025-05-30 21:07:30,081 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-30 21:07:30,081 - DEBUG - 思考模式已禁用（未设置thinkingConfig参数 或预算为0）
2025-05-30 21:07:30,081 - DEBUG - 明确禁用思考模式(generationConfig.thinkingConfig.thinkingBudget=0)
2025-05-30 21:07:30,949 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "出现了一些奇怪的词语呢"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 689,
    "candidatesTokenCount": 7,
    "totalTokenCount": 696,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 689
      }
    ]
  },
  "modelVersion": "models/gemini-2.5-flash-preview-04-17",
  "responseId": "gp85aPbFMLml1MkPmLiJ2As"
}

2025-05-30 21:07:30,950 - DEBUG - 【API响应JSON对象】模型: gemini-2.5-flash-preview-04-17, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '出现了一些奇怪的词语呢'}], 'role': 'model'}, 'finishReason': 'STOP', 'index': 0}], 'usageMetadata': {'promptTokenCount': 689, 'candidatesTokenCount': 7, 'totalTokenCount': 696, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 689}]}, 'modelVersion': 'models/gemini-2.5-flash-preview-04-17', 'responseId': 'gp85aPbFMLml1MkPmLiJ2As'}
2025-05-30 21:07:30,950 - DEBUG - 【Token使用情况】模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:07:30,951 - DEBUG -   - 思考Token数: 0
2025-05-30 21:07:30,951 - DEBUG -   - 提示Token数: 689
2025-05-30 21:07:30,951 - DEBUG -   - 输出Token数: 7
2025-05-30 21:07:30,951 - DEBUG -   - 总Token数: 696
2025-05-30 21:07:30,952 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 97, 1927.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:07:30,952 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.979)]
2025-05-30 21:07:30,953 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-30 21:07:30,953 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-30 21:07:30,953 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-30 21:07:30,953 - INFO - API翻译成功。模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:07:31,466 - DEBUG - 输入框内容已替换为: 出现了一些奇怪的词语呢
2025-05-30 21:07:31,467 - INFO - 【翻译结果】
出现了一些奇怪的词语呢
2025-05-30 21:07:31,467 - DEBUG - 缓存已满，移除最久未使用项: 2-zh-ko-667615903763...
2025-05-30 21:07:31,467 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-30 21:07:31,467 - DEBUG - 缓存已满，移除最久未使用项: 2-ko-zh--37403415056...
2025-05-30 21:07:31,467 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-30 21:07:31,468 - INFO - 翻译完成，结果已替换输入框内容
2025-05-30 21:07:31,470 - INFO - 已立即保存 2 条缓存记录
2025-05-30 21:07:31,474 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-30 21:08:08,779 - INFO - 检测到三次空格，触发翻译
2025-05-30 21:08:08,780 - INFO - 【原文】
哈哈~慢慢学习吧。要现在尝试一下么？叫我妈妈也许我会给你舔舔我的脚哦~
2025-05-30 21:08:08,781 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 98, 1910.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:08:08,781 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.952)]
2025-05-30 21:08:08,781 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-30 21:08:08,782 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-30 21:08:08,782 - INFO - 检测到原文语言: zh
2025-05-30 21:08:08,782 - INFO - 执行正向翻译为: ko
2025-05-30 21:08:08,782 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-30 21:08:08,782 - DEBUG - 检测到语言: zh, 输入语言: 中文(zh), 输出语言: 韩文(ko)
2025-05-30 21:08:08,783 - DEBUG - 使用语气词 - 输入: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜], 输出: [ㅋ|ㅎ|아|네|헤|ㅜ]
2025-05-30 21:08:08,783 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-30 21:08:08,783 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-30 21:08:08,783 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 中文 和输出为 韩文 ，将内容从 中文 翻译成 韩文 ，使用敬语
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 韩文，调整语气和风格，考虑文化内涵和地区差异。不得包含 中文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 中的语气助词时，才将其翻译为 韩文 中等效的 [ㅋ|ㅎ|아|네|헤|ㅜ] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 韩文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 韩文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: 무섭지 않으세요?ㅎ
翻译: 不害怕吗？嘿嘿
原文: 哈哈~骑太快我肯定会害怕。
翻译: ㅋ~ 너무 빨리 타면 분명 무서울 거예요.
原文: 하하! 감사합니다! 한번 조사는 해봤었습니다. 제가 아는 동생도 타투리스트였거든요. 그런데 그친구도 안좋은 일을 조금 겪고 힘들어 하더군요...
翻译: 哈哈！谢谢！我之前调查过一次。我认识的一个弟弟也是纹身师。但是那个朋友也遇到了一些不好的事情，过得很辛苦呢……
原文: 你可以抽时间去学习，然后自己开工作室，不要给别人打工才是正确的。
翻译: 시간 내서 배우고 직접 스튜디오를 여는 게 맞아요. 남 밑에서 일하지 마세요.
原文: 대만에선 몇년 지내셨어요?
翻译: 在台湾住了几年？
原文: 从小在台湾长大。经常来韩国探亲。
翻译: 어릴 때부터 대만에서 자랐습니다. 한국에는 친척 방문으로 자주 왔습니다.
原文: 你现在在做什么呢？
翻译: 지금 뭐 하고 계세요?
原文: 이상한 용어들이 나오는데요
翻译: 出现了一些奇怪的词语呢

将以下内容从中文翻译成韩文，使用敬语：
哈哈~慢慢学习吧。要现在尝试一下么？叫我妈妈也许我会给你舔舔我的脚哦~
2025-05-30 21:08:08,783 - DEBUG - 【构建提示词】长度: 1125 字符
2025-05-30 21:08:09,219 - DEBUG - API密钥解密成功
2025-05-30 21:08:09,219 - DEBUG - API密钥解密成功
2025-05-30 21:08:09,220 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-30 21:08:09,220 - DEBUG - 思考模式已禁用（未设置thinkingConfig参数 或预算为0）
2025-05-30 21:08:09,220 - DEBUG - 明确禁用思考模式(generationConfig.thinkingConfig.thinkingBudget=0)
2025-05-30 21:08:10,317 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "ㅋ~ 천천히 배우세요. 지금 한번 시도해 보실래요? 저를 엄마라고 부르면 제 발을 핥게 해줄지도 몰라요~"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 717,
    "candidatesTokenCount": 38,
    "totalTokenCount": 755,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 717
      }
    ]
  },
  "modelVersion": "models/gemini-2.5-flash-preview-04-17",
  "responseId": "qp85aMTEDP2B1MkPzdXMsQ8"
}

2025-05-30 21:08:10,318 - DEBUG - 【API响应JSON对象】模型: gemini-2.5-flash-preview-04-17, 响应JSON: {'candidates': [{'content': {'parts': [{'text': 'ㅋ~ 천천히 배우세요. 지금 한번 시도해 보실래요? 저를 엄마라고 부르면 제 발을 핥게 해줄지도 몰라요~'}], 'role': 'model'}, 'finishReason': 'STOP', 'index': 0}], 'usageMetadata': {'promptTokenCount': 717, 'candidatesTokenCount': 38, 'totalTokenCount': 755, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 717}]}, 'modelVersion': 'models/gemini-2.5-flash-preview-04-17', 'responseId': 'qp85aMTEDP2B1MkPzdXMsQ8'}
2025-05-30 21:08:10,319 - DEBUG - 【Token使用情况】模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:08:10,319 - DEBUG -   - 思考Token数: 0
2025-05-30 21:08:10,320 - DEBUG -   - 提示Token数: 717
2025-05-30 21:08:10,320 - DEBUG -   - 输出Token数: 38
2025-05-30 21:08:10,321 - DEBUG -   - 总Token数: 755
2025-05-30 21:08:10,322 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 99, 3640.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:08:10,322 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.895)]
2025-05-30 21:08:10,322 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-30 21:08:10,322 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-30 21:08:10,323 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-30 21:08:10,323 - INFO - API翻译成功。模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:08:10,853 - DEBUG - 输入框内容已替换为: ㅋ~ 천천히 배우세요. 지금 한번 시도해 보실래요? 저를 엄마라고 부르면 제 발을 핥게 해줄지도 몰라요~
2025-05-30 21:08:10,854 - INFO - 【翻译结果】
ㅋ~ 천천히 배우세요. 지금 한번 시도해 보실래요? 저를 엄마라고 부르면 제 발을 핥게 해줄지도 몰라요~
2025-05-30 21:08:10,855 - DEBUG - 缓存已满，移除最久未使用项: 2-ko-zh--34808926478...
2025-05-30 21:08:10,855 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-30 21:08:10,855 - DEBUG - 缓存已满，移除最久未使用项: 2-zh-ko--54422818730...
2025-05-30 21:08:10,855 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-30 21:08:10,856 - INFO - 翻译完成，结果已替换输入框内容
2025-05-30 21:08:10,858 - INFO - 已立即保存 2 条缓存记录
2025-05-30 21:08:10,861 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-30 21:09:43,580 - INFO - 检测到三次空格，触发翻译
2025-05-30 21:09:43,581 - INFO - 【原文】
저도 그러고 싶습니다 (umm) 그런데 아직은 잘안되네요.. 더늦기전에 그렇게 해야할텐데..
2025-05-30 21:09:43,582 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 93, 3754.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:09:43,582 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.845)]
2025-05-30 21:09:43,582 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-30 21:09:43,583 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-30 21:09:43,583 - INFO - 检测到原文语言: ko
2025-05-30 21:09:43,583 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-30 21:09:43,583 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-30 21:09:43,583 - DEBUG - 检测到输入语言 (ko) 与当前模式定义的目标语言 (ko) 一致，触发反向翻译流程。
2025-05-30 21:09:43,583 - DEBUG - 检测到语言: ko, 输入语言: 韩文(ko), 输出语言: 中文(zh)
2025-05-30 21:09:43,583 - DEBUG - 使用语气词 - 输入: [ㅋ|ㅎ|아|네|헤|ㅜ], 输出: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜]
2025-05-30 21:09:43,584 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-30 21:09:43,584 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-30 21:09:43,584 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: 哈哈~骑太快我肯定会害怕。
翻译: ㅋ~ 너무 빨리 타면 분명 무서울 거예요.
原文: 하하! 감사합니다! 한번 조사는 해봤었습니다. 제가 아는 동생도 타투리스트였거든요. 그런데 그친구도 안좋은 일을 조금 겪고 힘들어 하더군요...
翻译: 哈哈！谢谢！我之前调查过一次。我认识的一个弟弟也是纹身师。但是那个朋友也遇到了一些不好的事情，过得很辛苦呢……
原文: 你可以抽时间去学习，然后自己开工作室，不要给别人打工才是正确的。
翻译: 시간 내서 배우고 직접 스튜디오를 여는 게 맞아요. 남 밑에서 일하지 마세요.
原文: 대만에선 몇년 지내셨어요?
翻译: 在台湾住了几年？
原文: 从小在台湾长大。经常来韩国探亲。
翻译: 어릴 때부터 대만에서 자랐습니다. 한국에는 친척 방문으로 자주 왔습니다.
原文: 你现在在做什么呢？
翻译: 지금 뭐 하고 계세요?
原文: 이상한 용어들이 나오는데요
翻译: 出现了一些奇怪的词语呢
原文: 哈哈~慢慢学习吧。要现在尝试一下么？叫我妈妈也许我会给你舔舔我的脚哦~
翻译: ㅋ~ 천천히 배우세요. 지금 한번 시도해 보실래요? 저를 엄마라고 부르면 제 발을 핥게 해줄지도 몰라요~

将以下内容从韩文翻译成中文：
저도 그러고 싶습니다 (umm) 그런데 아직은 잘안되네요.. 더늦기전에 그렇게 해야할텐데..
2025-05-30 21:09:43,584 - DEBUG - 【构建提示词】长度: 1207 字符
2025-05-30 21:09:43,829 - DEBUG - API密钥解密成功
2025-05-30 21:09:43,829 - DEBUG - API密钥解密成功
2025-05-30 21:09:43,830 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-30 21:09:43,830 - DEBUG - 思考模式已禁用（未设置thinkingConfig参数 或预算为0）
2025-05-30 21:09:43,830 - DEBUG - 明确禁用思考模式(generationConfig.thinkingConfig.thinkingBudget=0)
2025-05-30 21:09:44,713 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "我也想那样（嗯）但是现在还不太行呢..得趁着还没太晚那样做才行.."
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 763,
    "candidatesTokenCount": 24,
    "totalTokenCount": 787,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 763
      }
    ]
  },
  "modelVersion": "models/gemini-2.5-flash-preview-04-17",
  "responseId": "CKA5aI7LJfqo1MkP1ZKAkAk"
}

2025-05-30 21:09:44,714 - DEBUG - 【API响应JSON对象】模型: gemini-2.5-flash-preview-04-17, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '我也想那样（嗯）但是现在还不太行呢..得趁着还没太晚那样做才行..'}], 'role': 'model'}, 'finishReason': 'STOP', 'index': 0}], 'usageMetadata': {'promptTokenCount': 763, 'candidatesTokenCount': 24, 'totalTokenCount': 787, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 763}]}, 'modelVersion': 'models/gemini-2.5-flash-preview-04-17', 'responseId': 'CKA5aI7LJfqo1MkP1ZKAkAk'}
2025-05-30 21:09:44,714 - DEBUG - 【Token使用情况】模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:09:44,714 - DEBUG -   - 思考Token数: 0
2025-05-30 21:09:44,714 - DEBUG -   - 提示Token数: 763
2025-05-30 21:09:44,715 - DEBUG -   - 输出Token数: 24
2025-05-30 21:09:44,715 - DEBUG -   - 总Token数: 787
2025-05-30 21:09:44,716 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 98, 1782.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:09:44,716 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.931)]
2025-05-30 21:09:44,717 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-30 21:09:44,717 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-30 21:09:44,717 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-30 21:09:44,717 - INFO - API翻译成功。模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:09:45,265 - DEBUG - 输入框内容已替换为: 我也想那样（嗯）但是现在还不太行呢..得趁着还没太晚那样做才行..
2025-05-30 21:09:45,267 - INFO - 【翻译结果】
我也想那样（嗯）但是现在还不太行呢..得趁着还没太晚那样做才行..
2025-05-30 21:09:45,267 - DEBUG - 缓存已满，移除最久未使用项: 2-zh-ko-284507906187...
2025-05-30 21:09:45,268 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-30 21:09:45,268 - DEBUG - 缓存已满，移除最久未使用项: 2-ko-zh--71762181774...
2025-05-30 21:09:45,268 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-30 21:09:45,269 - INFO - 翻译完成，结果已替换输入框内容
2025-05-30 21:09:45,272 - INFO - 已立即保存 2 条缓存记录
2025-05-30 21:09:45,275 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-30 21:09:54,285 - INFO - 检测到三次空格，触发翻译
2025-05-30 21:09:54,286 - INFO - 【原文】
弟弟有创业的打算么？
2025-05-30 21:09:54,287 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 96, 2011.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:09:54,287 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.942)]
2025-05-30 21:09:54,287 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-30 21:09:54,288 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-30 21:09:54,288 - INFO - 检测到原文语言: zh
2025-05-30 21:09:54,288 - INFO - 执行正向翻译为: ko
2025-05-30 21:09:54,289 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-30 21:09:54,289 - DEBUG - 检测到语言: zh, 输入语言: 中文(zh), 输出语言: 韩文(ko)
2025-05-30 21:09:54,289 - DEBUG - 使用语气词 - 输入: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜], 输出: [ㅋ|ㅎ|아|네|헤|ㅜ]
2025-05-30 21:09:54,289 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-30 21:09:54,290 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-30 21:09:54,290 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 中文 和输出为 韩文 ，将内容从 中文 翻译成 韩文 ，使用敬语
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 韩文，调整语气和风格，考虑文化内涵和地区差异。不得包含 中文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 中的语气助词时，才将其翻译为 韩文 中等效的 [ㅋ|ㅎ|아|네|헤|ㅜ] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 韩文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 韩文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: 하하! 감사합니다! 한번 조사는 해봤었습니다. 제가 아는 동생도 타투리스트였거든요. 그런데 그친구도 안좋은 일을 조금 겪고 힘들어 하더군요...
翻译: 哈哈！谢谢！我之前调查过一次。我认识的一个弟弟也是纹身师。但是那个朋友也遇到了一些不好的事情，过得很辛苦呢……
原文: 你可以抽时间去学习，然后自己开工作室，不要给别人打工才是正确的。
翻译: 시간 내서 배우고 직접 스튜디오를 여는 게 맞아요. 남 밑에서 일하지 마세요.
原文: 대만에선 몇년 지내셨어요?
翻译: 在台湾住了几年？
原文: 从小在台湾长大。经常来韩国探亲。
翻译: 어릴 때부터 대만에서 자랐습니다. 한국에는 친척 방문으로 자주 왔습니다.
原文: 你现在在做什么呢？
翻译: 지금 뭐 하고 계세요?
原文: 이상한 용어들이 나오는데요
翻译: 出现了一些奇怪的词语呢
原文: 哈哈~慢慢学习吧。要现在尝试一下么？叫我妈妈也许我会给你舔舔我的脚哦~
翻译: ㅋ~ 천천히 배우세요. 지금 한번 시도해 보실래요? 저를 엄마라고 부르면 제 발을 핥게 해줄지도 몰라요~
原文: 저도 그러고 싶습니다 (umm) 그런데 아직은 잘안되네요.. 더늦기전에 그렇게 해야할텐데..
翻译: 我也想那样（嗯）但是现在还不太行呢..得趁着还没太晚那样做才行..

将以下内容从中文翻译成韩文，使用敬语：
弟弟有创业的打算么？
2025-05-30 21:09:54,291 - DEBUG - 【构建提示词】长度: 1224 字符
2025-05-30 21:09:54,406 - DEBUG - API密钥解密成功
2025-05-30 21:09:54,407 - DEBUG - API密钥解密成功
2025-05-30 21:09:54,407 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-30 21:09:54,407 - DEBUG - 思考模式已禁用（未设置thinkingConfig参数 或预算为0）
2025-05-30 21:09:54,408 - DEBUG - 明确禁用思考模式(generationConfig.thinkingConfig.thinkingBudget=0)
2025-05-30 21:09:55,511 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "동생분은 창업할 생각이 있으신가요?"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 779,
    "candidatesTokenCount": 13,
    "totalTokenCount": 792,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 779
      }
    ]
  },
  "modelVersion": "models/gemini-2.5-flash-preview-04-17",
  "responseId": "E6A5aKzbDP2d1MkPtN6EwQQ"
}

2025-05-30 21:09:55,512 - DEBUG - 【API响应JSON对象】模型: gemini-2.5-flash-preview-04-17, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '동생분은 창업할 생각이 있으신가요?'}], 'role': 'model'}, 'finishReason': 'STOP', 'index': 0}], 'usageMetadata': {'promptTokenCount': 779, 'candidatesTokenCount': 13, 'totalTokenCount': 792, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 779}]}, 'modelVersion': 'models/gemini-2.5-flash-preview-04-17', 'responseId': 'E6A5aKzbDP2d1MkPtN6EwQQ'}
2025-05-30 21:09:55,512 - DEBUG - 【Token使用情况】模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:09:55,513 - DEBUG -   - 思考Token数: 0
2025-05-30 21:09:55,513 - DEBUG -   - 提示Token数: 779
2025-05-30 21:09:55,513 - DEBUG -   - 输出Token数: 13
2025-05-30 21:09:55,513 - DEBUG -   - 总Token数: 792
2025-05-30 21:09:55,514 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 98, 3761.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:09:55,514 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.923)]
2025-05-30 21:09:55,514 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-30 21:09:55,514 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-30 21:09:55,514 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-30 21:09:55,514 - INFO - API翻译成功。模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:09:56,021 - DEBUG - 输入框内容已替换为: 동생분은 창업할 생각이 있으신가요?
2025-05-30 21:09:56,022 - INFO - 【翻译结果】
동생분은 창업할 생각이 있으신가요?
2025-05-30 21:09:56,022 - DEBUG - 缓存已满，移除最久未使用项: 2-ko-zh-515247069626...
2025-05-30 21:09:56,022 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-30 21:09:56,023 - DEBUG - 缓存已满，移除最久未使用项: 2-zh-ko-347147205561...
2025-05-30 21:09:56,023 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-30 21:09:56,024 - INFO - 翻译完成，结果已替换输入框内容
2025-05-30 21:09:56,025 - INFO - 已立即保存 2 条缓存记录
2025-05-30 21:09:56,028 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-30 21:09:58,949 - INFO - 检测到三次空格，触发翻译
2025-05-30 21:09:58,950 - INFO - 【原文】
한국말을 잘 하시네요. 어머니가 가르쳐 주신거에요?
2025-05-30 21:09:58,951 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 98, 3739.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:09:58,951 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.911)]
2025-05-30 21:09:58,952 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-30 21:09:58,952 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-30 21:09:58,952 - INFO - 检测到原文语言: ko
2025-05-30 21:09:58,952 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-30 21:09:58,952 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-30 21:09:58,952 - DEBUG - 检测到输入语言 (ko) 与当前模式定义的目标语言 (ko) 一致，触发反向翻译流程。
2025-05-30 21:09:58,952 - DEBUG - 检测到语言: ko, 输入语言: 韩文(ko), 输出语言: 中文(zh)
2025-05-30 21:09:58,952 - DEBUG - 使用语气词 - 输入: [ㅋ|ㅎ|아|네|헤|ㅜ], 输出: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜]
2025-05-30 21:09:58,953 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-30 21:09:58,953 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-30 21:09:58,953 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: 你可以抽时间去学习，然后自己开工作室，不要给别人打工才是正确的。
翻译: 시간 내서 배우고 직접 스튜디오를 여는 게 맞아요. 남 밑에서 일하지 마세요.
原文: 대만에선 몇년 지내셨어요?
翻译: 在台湾住了几年？
原文: 从小在台湾长大。经常来韩国探亲。
翻译: 어릴 때부터 대만에서 자랐습니다. 한국에는 친척 방문으로 자주 왔습니다.
原文: 你现在在做什么呢？
翻译: 지금 뭐 하고 계세요?
原文: 이상한 용어들이 나오는데요
翻译: 出现了一些奇怪的词语呢
原文: 哈哈~慢慢学习吧。要现在尝试一下么？叫我妈妈也许我会给你舔舔我的脚哦~
翻译: ㅋ~ 천천히 배우세요. 지금 한번 시도해 보실래요? 저를 엄마라고 부르면 제 발을 핥게 해줄지도 몰라요~
原文: 저도 그러고 싶습니다 (umm) 그런데 아직은 잘안되네요.. 더늦기전에 그렇게 해야할텐데..
翻译: 我也想那样（嗯）但是现在还不太行呢..得趁着还没太晚那样做才行..
原文: 弟弟有创业的打算么？
翻译: 동생분은 창업할 생각이 있으신가요?

将以下内容从韩文翻译成中文：
한국말을 잘 하시네요. 어머니가 가르쳐 주신거에요?
2025-05-30 21:09:58,953 - DEBUG - 【构建提示词】长度: 1126 字符
2025-05-30 21:09:59,054 - DEBUG - API密钥解密成功
2025-05-30 21:09:59,055 - DEBUG - API密钥解密成功
2025-05-30 21:09:59,055 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-30 21:09:59,056 - DEBUG - 思考模式已禁用（未设置thinkingConfig参数 或预算为0）
2025-05-30 21:09:59,056 - DEBUG - 明确禁用思考模式(generationConfig.thinkingConfig.thinkingBudget=0)
2025-05-30 21:09:59,984 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "你的韩语说得真好。是你妈妈教的吗？"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 725,
    "candidatesTokenCount": 14,
    "totalTokenCount": 739,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 725
      }
    ]
  },
  "modelVersion": "models/gemini-2.5-flash-preview-04-17",
  "responseId": "F6A5aM6SNpav1MkPva6_8Q0"
}

2025-05-30 21:09:59,985 - DEBUG - 【API响应JSON对象】模型: gemini-2.5-flash-preview-04-17, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '你的韩语说得真好。是你妈妈教的吗？'}], 'role': 'model'}, 'finishReason': 'STOP', 'index': 0}], 'usageMetadata': {'promptTokenCount': 725, 'candidatesTokenCount': 14, 'totalTokenCount': 739, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 725}]}, 'modelVersion': 'models/gemini-2.5-flash-preview-04-17', 'responseId': 'F6A5aM6SNpav1MkPva6_8Q0'}
2025-05-30 21:09:59,985 - DEBUG - 【Token使用情况】模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:09:59,986 - DEBUG -   - 思考Token数: 0
2025-05-30 21:09:59,986 - DEBUG -   - 提示Token数: 725
2025-05-30 21:09:59,986 - DEBUG -   - 输出Token数: 14
2025-05-30 21:09:59,986 - DEBUG -   - 总Token数: 739
2025-05-30 21:09:59,987 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 97, 1960.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:09:59,987 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.944)]
2025-05-30 21:09:59,987 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-30 21:09:59,988 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-30 21:09:59,988 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-30 21:09:59,988 - INFO - API翻译成功。模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:10:00,514 - DEBUG - 输入框内容已替换为: 你的韩语说得真好。是你妈妈教的吗？
2025-05-30 21:10:00,515 - INFO - 【翻译结果】
你的韩语说得真好。是你妈妈教的吗？
2025-05-30 21:10:00,516 - DEBUG - 缓存已满，移除最久未使用项: 2-zh-ko-231179225147...
2025-05-30 21:10:00,516 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-30 21:10:00,517 - DEBUG - 缓存已满，移除最久未使用项: 2-ko-zh-891433826061...
2025-05-30 21:10:00,517 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-30 21:10:00,518 - INFO - 翻译完成，结果已替换输入框内容
2025-05-30 21:10:00,519 - INFO - 已立即保存 2 条缓存记录
2025-05-30 21:10:00,523 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-30 21:10:14,461 - INFO - 检测到三次空格，触发翻译
2025-05-30 21:10:14,462 - INFO - 【原文】
미화을 엄마라고 요
2025-05-30 21:10:14,463 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 96, 3640.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:10:14,463 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.912)]
2025-05-30 21:10:14,464 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-30 21:10:14,464 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-30 21:10:14,464 - INFO - 检测到原文语言: ko
2025-05-30 21:10:14,464 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-30 21:10:14,464 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-30 21:10:14,464 - DEBUG - 检测到输入语言 (ko) 与当前模式定义的目标语言 (ko) 一致，触发反向翻译流程。
2025-05-30 21:10:14,465 - DEBUG - 检测到语言: ko, 输入语言: 韩文(ko), 输出语言: 中文(zh)
2025-05-30 21:10:14,465 - DEBUG - 使用语气词 - 输入: [ㅋ|ㅎ|아|네|헤|ㅜ], 输出: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜]
2025-05-30 21:10:14,465 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-30 21:10:14,465 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-30 21:10:14,465 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: 대만에선 몇년 지내셨어요?
翻译: 在台湾住了几年？
原文: 从小在台湾长大。经常来韩国探亲。
翻译: 어릴 때부터 대만에서 자랐습니다. 한국에는 친척 방문으로 자주 왔습니다.
原文: 你现在在做什么呢？
翻译: 지금 뭐 하고 계세요?
原文: 이상한 용어들이 나오는데요
翻译: 出现了一些奇怪的词语呢
原文: 哈哈~慢慢学习吧。要现在尝试一下么？叫我妈妈也许我会给你舔舔我的脚哦~
翻译: ㅋ~ 천천히 배우세요. 지금 한번 시도해 보실래요? 저를 엄마라고 부르면 제 발을 핥게 해줄지도 몰라요~
原文: 저도 그러고 싶습니다 (umm) 그런데 아직은 잘안되네요.. 더늦기전에 그렇게 해야할텐데..
翻译: 我也想那样（嗯）但是现在还不太行呢..得趁着还没太晚那样做才行..
原文: 弟弟有创业的打算么？
翻译: 동생분은 창업할 생각이 있으신가요?
原文: 한국말을 잘 하시네요. 어머니가 가르쳐 주신거에요?
翻译: 你的韩语说得真好。是你妈妈教的吗？

将以下内容从韩文翻译成中文：
미화을 엄마라고 요
2025-05-30 21:10:14,465 - DEBUG - 【构建提示词】长度: 1078 字符
2025-05-30 21:10:14,652 - DEBUG - API密钥解密成功
2025-05-30 21:10:14,653 - DEBUG - API密钥解密成功
2025-05-30 21:10:14,653 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-30 21:10:14,653 - DEBUG - 思考模式已禁用（未设置thinkingConfig参数 或预算为0）
2025-05-30 21:10:14,653 - DEBUG - 明确禁用思考模式(generationConfig.thinkingConfig.thinkingBudget=0)
2025-05-30 21:10:15,511 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "叫美华妈妈呀"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 703,
    "candidatesTokenCount": 5,
    "totalTokenCount": 708,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 703
      }
    ]
  },
  "modelVersion": "models/gemini-2.5-flash-preview-04-17",
  "responseId": "J6A5aJDHF_PQ1MkPwZeY4Q4"
}

2025-05-30 21:10:15,511 - DEBUG - 【API响应JSON对象】模型: gemini-2.5-flash-preview-04-17, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '叫美华妈妈呀'}], 'role': 'model'}, 'finishReason': 'STOP', 'index': 0}], 'usageMetadata': {'promptTokenCount': 703, 'candidatesTokenCount': 5, 'totalTokenCount': 708, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 703}]}, 'modelVersion': 'models/gemini-2.5-flash-preview-04-17', 'responseId': 'J6A5aJDHF_PQ1MkPwZeY4Q4'}
2025-05-30 21:10:15,512 - DEBUG - 【Token使用情况】模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:10:15,512 - DEBUG -   - 思考Token数: 0
2025-05-30 21:10:15,512 - DEBUG -   - 提示Token数: 703
2025-05-30 21:10:15,512 - DEBUG -   - 输出Token数: 5
2025-05-30 21:10:15,512 - DEBUG -   - 总Token数: 708
2025-05-30 21:10:15,513 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 95, 1994.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:10:15,513 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.98)]
2025-05-30 21:10:15,514 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-30 21:10:15,514 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-30 21:10:15,514 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-30 21:10:15,514 - INFO - API翻译成功。模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:10:16,027 - DEBUG - 输入框内容已替换为: 叫美华妈妈呀
2025-05-30 21:10:16,028 - INFO - 【翻译结果】
叫美华妈妈呀
2025-05-30 21:10:16,028 - DEBUG - 缓存已满，移除最久未使用项: 2-ko-zh-427215670324...
2025-05-30 21:10:16,029 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-30 21:10:16,029 - DEBUG - 缓存已满，移除最久未使用项: 2-zh-ko--73094044988...
2025-05-30 21:10:16,029 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-30 21:10:16,030 - INFO - 翻译完成，结果已替换输入框内容
2025-05-30 21:10:16,031 - INFO - 已立即保存 2 条缓存记录
2025-05-30 21:10:16,035 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-30 21:10:33,078 - INFO - 检测到三次空格，触发翻译
2025-05-30 21:10:33,079 - INFO - 【原文】
啊。我的小狗~你想轻吻妈妈的脚么？
2025-05-30 21:10:33,080 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 97, 1865.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:10:33,081 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.926)]
2025-05-30 21:10:33,081 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-30 21:10:33,081 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-30 21:10:33,081 - INFO - 检测到原文语言: zh
2025-05-30 21:10:33,081 - INFO - 执行正向翻译为: ko
2025-05-30 21:10:33,081 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-30 21:10:33,081 - DEBUG - 检测到语言: zh, 输入语言: 中文(zh), 输出语言: 韩文(ko)
2025-05-30 21:10:33,082 - DEBUG - 使用语气词 - 输入: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜], 输出: [ㅋ|ㅎ|아|네|헤|ㅜ]
2025-05-30 21:10:33,082 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-30 21:10:33,082 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-30 21:10:33,082 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 中文 和输出为 韩文 ，将内容从 中文 翻译成 韩文 ，使用敬语
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 韩文，调整语气和风格，考虑文化内涵和地区差异。不得包含 中文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 中的语气助词时，才将其翻译为 韩文 中等效的 [ㅋ|ㅎ|아|네|헤|ㅜ] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 韩文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 韩文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: 从小在台湾长大。经常来韩国探亲。
翻译: 어릴 때부터 대만에서 자랐습니다. 한국에는 친척 방문으로 자주 왔습니다.
原文: 你现在在做什么呢？
翻译: 지금 뭐 하고 계세요?
原文: 이상한 용어들이 나오는데요
翻译: 出现了一些奇怪的词语呢
原文: 哈哈~慢慢学习吧。要现在尝试一下么？叫我妈妈也许我会给你舔舔我的脚哦~
翻译: ㅋ~ 천천히 배우세요. 지금 한번 시도해 보실래요? 저를 엄마라고 부르면 제 발을 핥게 해줄지도 몰라요~
原文: 저도 그러고 싶습니다 (umm) 그런데 아직은 잘안되네요.. 더늦기전에 그렇게 해야할텐데..
翻译: 我也想那样（嗯）但是现在还不太行呢..得趁着还没太晚那样做才行..
原文: 弟弟有创业的打算么？
翻译: 동생분은 창업할 생각이 있으신가요?
原文: 한국말을 잘 하시네요. 어머니가 가르쳐 주신거에요?
翻译: 你的韩语说得真好。是你妈妈教的吗？
原文: 미화을 엄마라고 요
翻译: 叫美华妈妈呀

将以下内容从中文翻译成韩文，使用敬语：
啊。我的小狗~你想轻吻妈妈的脚么？
2025-05-30 21:10:33,082 - DEBUG - 【构建提示词】长度: 1089 字符
2025-05-30 21:10:33,188 - DEBUG - API密钥解密成功
2025-05-30 21:10:33,189 - DEBUG - API密钥解密成功
2025-05-30 21:10:33,189 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-30 21:10:33,189 - DEBUG - 思考模式已禁用（未设置thinkingConfig参数 或预算为0）
2025-05-30 21:10:33,190 - DEBUG - 明确禁用思考模式(generationConfig.thinkingConfig.thinkingBudget=0)
2025-05-30 21:10:34,238 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "아. 내 강아지~ 엄마 발에 가볍게 입 맞추고 싶니?"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 715,
    "candidatesTokenCount": 20,
    "totalTokenCount": 735,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 715
      }
    ]
  },
  "modelVersion": "models/gemini-2.5-flash-preview-04-17",
  "responseId": "OqA5aOskl_66B5Op7OkK"
}

2025-05-30 21:10:34,238 - DEBUG - 【API响应JSON对象】模型: gemini-2.5-flash-preview-04-17, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '아. 내 강아지~ 엄마 발에 가볍게 입 맞추고 싶니?'}], 'role': 'model'}, 'finishReason': 'STOP', 'index': 0}], 'usageMetadata': {'promptTokenCount': 715, 'candidatesTokenCount': 20, 'totalTokenCount': 735, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 715}]}, 'modelVersion': 'models/gemini-2.5-flash-preview-04-17', 'responseId': 'OqA5aOskl_66B5Op7OkK'}
2025-05-30 21:10:34,238 - DEBUG - 【Token使用情况】模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:10:34,239 - DEBUG -   - 思考Token数: 0
2025-05-30 21:10:34,239 - DEBUG -   - 提示Token数: 715
2025-05-30 21:10:34,239 - DEBUG -   - 输出Token数: 20
2025-05-30 21:10:34,239 - DEBUG -   - 总Token数: 735
2025-05-30 21:10:34,240 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 98, 3510.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:10:34,240 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.872)]
2025-05-30 21:10:34,240 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-30 21:10:34,240 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-30 21:10:34,240 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-30 21:10:34,240 - INFO - API翻译成功。模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:10:34,767 - DEBUG - 输入框内容已替换为: 아. 내 강아지~ 엄마 발에 가볍게 입 맞추고 싶니?
2025-05-30 21:10:34,768 - INFO - 【翻译结果】
아. 내 강아지~ 엄마 발에 가볍게 입 맞추고 싶니?
2025-05-30 21:10:34,769 - DEBUG - 缓存已满，移除最久未使用项: 2-zh-ko--66543507066...
2025-05-30 21:10:34,769 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-30 21:10:34,770 - DEBUG - 缓存已满，移除最久未使用项: 2-ko-zh--50830831699...
2025-05-30 21:10:34,770 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-30 21:10:34,770 - INFO - 翻译完成，结果已替换输入框内容
2025-05-30 21:10:34,772 - INFO - 已立即保存 2 条缓存记录
2025-05-30 21:10:34,776 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-30 21:11:56,271 - INFO - 检测到三次空格，触发翻译
2025-05-30 21:11:56,272 - INFO - 【原文】
아이템이 있으면 한번 도전해보겠죠?!?
2025-05-30 21:11:56,273 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 98, 3761.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:11:56,273 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.9)]
2025-05-30 21:11:56,273 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-30 21:11:56,274 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-30 21:11:56,274 - INFO - 检测到原文语言: ko
2025-05-30 21:11:56,274 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-30 21:11:56,274 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-30 21:11:56,274 - DEBUG - 检测到输入语言 (ko) 与当前模式定义的目标语言 (ko) 一致，触发反向翻译流程。
2025-05-30 21:11:56,274 - DEBUG - 检测到语言: ko, 输入语言: 韩文(ko), 输出语言: 中文(zh)
2025-05-30 21:11:56,274 - DEBUG - 使用语气词 - 输入: [ㅋ|ㅎ|아|네|헤|ㅜ], 输出: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜]
2025-05-30 21:11:56,274 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-30 21:11:56,275 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-30 21:11:56,275 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: 你现在在做什么呢？
翻译: 지금 뭐 하고 계세요?
原文: 이상한 용어들이 나오는데요
翻译: 出现了一些奇怪的词语呢
原文: 哈哈~慢慢学习吧。要现在尝试一下么？叫我妈妈也许我会给你舔舔我的脚哦~
翻译: ㅋ~ 천천히 배우세요. 지금 한번 시도해 보실래요? 저를 엄마라고 부르면 제 발을 핥게 해줄지도 몰라요~
原文: 저도 그러고 싶습니다 (umm) 그런데 아직은 잘안되네요.. 더늦기전에 그렇게 해야할텐데..
翻译: 我也想那样（嗯）但是现在还不太行呢..得趁着还没太晚那样做才行..
原文: 弟弟有创业的打算么？
翻译: 동생분은 창업할 생각이 있으신가요?
原文: 한국말을 잘 하시네요. 어머니가 가르쳐 주신거에요?
翻译: 你的韩语说得真好。是你妈妈教的吗？
原文: 미화을 엄마라고 요
翻译: 叫美华妈妈呀
原文: 啊。我的小狗~你想轻吻妈妈的脚么？
翻译: 아. 내 강아지~ 엄마 발에 가볍게 입 맞추고 싶니?

将以下内容从韩文翻译成中文：
아이템이 있으면 한번 도전해보겠죠?!?
2025-05-30 21:11:56,275 - DEBUG - 【构建提示词】长度: 1073 字符
2025-05-30 21:11:56,493 - DEBUG - API密钥解密成功
2025-05-30 21:11:56,493 - DEBUG - API密钥解密成功
2025-05-30 21:11:56,493 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-30 21:11:56,493 - DEBUG - 思考模式已禁用（未设置thinkingConfig参数 或预算为0）
2025-05-30 21:11:56,494 - DEBUG - 明确禁用思考模式(generationConfig.thinkingConfig.thinkingBudget=0)
2025-05-30 21:11:57,371 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "要是有好的项目，肯定会去尝试一下的吧？！"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 704,
    "candidatesTokenCount": 13,
    "totalTokenCount": 717,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 704
      }
    ]
  },
  "modelVersion": "models/gemini-2.5-flash-preview-04-17",
  "responseId": "jaA5aOGWEdnT_uMPk8XwyQM"
}

2025-05-30 21:11:57,371 - DEBUG - 【API响应JSON对象】模型: gemini-2.5-flash-preview-04-17, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '要是有好的项目，肯定会去尝试一下的吧？！'}], 'role': 'model'}, 'finishReason': 'STOP', 'index': 0}], 'usageMetadata': {'promptTokenCount': 704, 'candidatesTokenCount': 13, 'totalTokenCount': 717, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 704}]}, 'modelVersion': 'models/gemini-2.5-flash-preview-04-17', 'responseId': 'jaA5aOGWEdnT_uMPk8XwyQM'}
2025-05-30 21:11:57,372 - DEBUG - 【Token使用情况】模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:11:57,372 - DEBUG -   - 思考Token数: 0
2025-05-30 21:11:57,372 - DEBUG -   - 提示Token数: 704
2025-05-30 21:11:57,372 - DEBUG -   - 输出Token数: 13
2025-05-30 21:11:57,372 - DEBUG -   - 总Token数: 717
2025-05-30 21:11:57,373 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 98, 1932.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:11:57,373 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.941)]
2025-05-30 21:11:57,373 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-30 21:11:57,374 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-30 21:11:57,374 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-30 21:11:57,374 - INFO - API翻译成功。模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:11:57,887 - DEBUG - 输入框内容已替换为: 要是有好的项目，肯定会去尝试一下的吧？！
2025-05-30 21:11:57,888 - INFO - 【翻译结果】
要是有好的项目，肯定会去尝试一下的吧？！
2025-05-30 21:11:57,889 - DEBUG - 缓存已满，移除最久未使用项: 2-ko-zh-101956569212...
2025-05-30 21:11:57,890 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-30 21:11:57,890 - DEBUG - 缓存已满，移除最久未使用项: 2-zh-ko--87862193668...
2025-05-30 21:11:57,890 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-30 21:11:57,892 - INFO - 翻译完成，结果已替换输入框内容
2025-05-30 21:11:57,893 - INFO - 已立即保存 2 条缓存记录
2025-05-30 21:11:57,897 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-30 21:12:47,936 - INFO - 检测到三次空格，触发翻译
2025-05-30 21:12:47,937 - INFO - 【原文】
啊，但是即便是有好项目你也不会投资的，因为你没钱，哈哈。
2025-05-30 21:12:47,938 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 98, 1886.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:12:47,938 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.943)]
2025-05-30 21:12:47,939 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-30 21:12:47,939 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-30 21:12:47,939 - INFO - 检测到原文语言: zh
2025-05-30 21:12:47,939 - INFO - 执行正向翻译为: ko
2025-05-30 21:12:47,940 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-30 21:12:47,940 - DEBUG - 检测到语言: zh, 输入语言: 中文(zh), 输出语言: 韩文(ko)
2025-05-30 21:12:47,940 - DEBUG - 使用语气词 - 输入: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜], 输出: [ㅋ|ㅎ|아|네|헤|ㅜ]
2025-05-30 21:12:47,940 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-30 21:12:47,940 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-30 21:12:47,940 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 中文 和输出为 韩文 ，将内容从 中文 翻译成 韩文 ，使用敬语
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 韩文，调整语气和风格，考虑文化内涵和地区差异。不得包含 中文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 中的语气助词时，才将其翻译为 韩文 中等效的 [ㅋ|ㅎ|아|네|헤|ㅜ] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 韩文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 韩文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: 이상한 용어들이 나오는데요
翻译: 出现了一些奇怪的词语呢
原文: 哈哈~慢慢学习吧。要现在尝试一下么？叫我妈妈也许我会给你舔舔我的脚哦~
翻译: ㅋ~ 천천히 배우세요. 지금 한번 시도해 보실래요? 저를 엄마라고 부르면 제 발을 핥게 해줄지도 몰라요~
原文: 저도 그러고 싶습니다 (umm) 그런데 아직은 잘안되네요.. 더늦기전에 그렇게 해야할텐데..
翻译: 我也想那样（嗯）但是现在还不太行呢..得趁着还没太晚那样做才行..
原文: 弟弟有创业的打算么？
翻译: 동생분은 창업할 생각이 있으신가요?
原文: 한국말을 잘 하시네요. 어머니가 가르쳐 주신거에요?
翻译: 你的韩语说得真好。是你妈妈教的吗？
原文: 미화을 엄마라고 요
翻译: 叫美华妈妈呀
原文: 啊。我的小狗~你想轻吻妈妈的脚么？
翻译: 아. 내 강아지~ 엄마 발에 가볍게 입 맞추고 싶니?
原文: 아이템이 있으면 한번 도전해보겠죠?!?
翻译: 要是有好的项目，肯定会去尝试一下的吧？！

将以下内容从中文翻译成韩文，使用敬语：
啊，但是即便是有好项目你也不会投资的，因为你没钱，哈哈。
2025-05-30 21:12:47,940 - DEBUG - 【构建提示词】长度: 1110 字符
2025-05-30 21:12:48,179 - DEBUG - API密钥解密成功
2025-05-30 21:12:48,180 - DEBUG - API密钥解密成功
2025-05-30 21:12:48,180 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-30 21:12:48,180 - DEBUG - 思考模式已禁用（未设置thinkingConfig参数 或预算为0）
2025-05-30 21:12:48,180 - DEBUG - 明确禁用思考模式(generationConfig.thinkingConfig.thinkingBudget=0)
2025-05-30 21:12:48,971 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "아, 하지만 좋은 프로젝트가 있어도 투자하지 않으실 거예요, 돈이 없으시니까요, ㅋ."
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 731,
    "candidatesTokenCount": 26,
    "totalTokenCount": 757,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 731
      }
    ]
  },
  "modelVersion": "models/gemini-2.5-flash-preview-04-17",
  "responseId": "wKA5aMueNLni_uMPlrv8EA"
}

2025-05-30 21:12:48,971 - DEBUG - 【API响应JSON对象】模型: gemini-2.5-flash-preview-04-17, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '아, 하지만 좋은 프로젝트가 있어도 투자하지 않으실 거예요, 돈이 없으시니까요, ㅋ.'}], 'role': 'model'}, 'finishReason': 'STOP', 'index': 0}], 'usageMetadata': {'promptTokenCount': 731, 'candidatesTokenCount': 26, 'totalTokenCount': 757, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 731}]}, 'modelVersion': 'models/gemini-2.5-flash-preview-04-17', 'responseId': 'wKA5aMueNLni_uMPlrv8EA'}
2025-05-30 21:12:48,972 - DEBUG - 【Token使用情况】模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:12:48,972 - DEBUG -   - 思考Token数: 0
2025-05-30 21:12:48,972 - DEBUG -   - 提示Token数: 731
2025-05-30 21:12:48,973 - DEBUG -   - 输出Token数: 26
2025-05-30 21:12:48,973 - DEBUG -   - 总Token数: 757
2025-05-30 21:12:48,974 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 99, 3686.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:12:48,974 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.897)]
2025-05-30 21:12:48,974 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-30 21:12:48,974 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-30 21:12:48,974 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-30 21:12:48,975 - INFO - API翻译成功。模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:12:49,482 - DEBUG - 输入框内容已替换为: 아, 하지만 좋은 프로젝트가 있어도 투자하지 않으실 거예요, 돈이 없으시니까요, ㅋ.
2025-05-30 21:12:49,482 - INFO - 【翻译结果】
아, 하지만 좋은 프로젝트가 있어도 투자하지 않으실 거예요, 돈이 없으시니까요, ㅋ.
2025-05-30 21:12:49,483 - DEBUG - 缓存已满，移除最久未使用项: 2-ko-zh-523109212227...
2025-05-30 21:12:49,484 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-30 21:12:49,484 - DEBUG - 缓存已满，移除最久未使用项: 2-zh-ko-426902589561...
2025-05-30 21:12:49,484 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-30 21:12:49,485 - INFO - 翻译完成，结果已替换输入框内容
2025-05-30 21:12:49,487 - INFO - 已立即保存 2 条缓存记录
2025-05-30 21:12:49,490 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-30 21:13:16,073 - INFO - 检测到三次空格，触发翻译
2025-05-30 21:13:16,074 - INFO - 【原文】
散步回来了。啊，天气真好呢。
2025-05-30 21:13:16,075 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 97, 1763.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:13:16,076 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.915)]
2025-05-30 21:13:16,076 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-30 21:13:16,076 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-30 21:13:16,076 - INFO - 检测到原文语言: zh
2025-05-30 21:13:16,076 - INFO - 执行正向翻译为: ko
2025-05-30 21:13:16,076 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-30 21:13:16,076 - DEBUG - 检测到语言: zh, 输入语言: 中文(zh), 输出语言: 韩文(ko)
2025-05-30 21:13:16,076 - DEBUG - 使用语气词 - 输入: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜], 输出: [ㅋ|ㅎ|아|네|헤|ㅜ]
2025-05-30 21:13:16,077 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-30 21:13:16,077 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-30 21:13:16,077 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 中文 和输出为 韩文 ，将内容从 中文 翻译成 韩文 ，使用敬语
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 韩文，调整语气和风格，考虑文化内涵和地区差异。不得包含 中文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 中的语气助词时，才将其翻译为 韩文 中等效的 [ㅋ|ㅎ|아|네|헤|ㅜ] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 韩文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 韩文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: 哈哈~慢慢学习吧。要现在尝试一下么？叫我妈妈也许我会给你舔舔我的脚哦~
翻译: ㅋ~ 천천히 배우세요. 지금 한번 시도해 보실래요? 저를 엄마라고 부르면 제 발을 핥게 해줄지도 몰라요~
原文: 저도 그러고 싶습니다 (umm) 그런데 아직은 잘안되네요.. 더늦기전에 그렇게 해야할텐데..
翻译: 我也想那样（嗯）但是现在还不太行呢..得趁着还没太晚那样做才行..
原文: 弟弟有创业的打算么？
翻译: 동생분은 창업할 생각이 있으신가요?
原文: 한국말을 잘 하시네요. 어머니가 가르쳐 주신거에요?
翻译: 你的韩语说得真好。是你妈妈教的吗？
原文: 미화을 엄마라고 요
翻译: 叫美华妈妈呀
原文: 啊。我的小狗~你想轻吻妈妈的脚么？
翻译: 아. 내 강아지~ 엄마 발에 가볍게 입 맞추고 싶니?
原文: 아이템이 있으면 한번 도전해보겠죠?!?
翻译: 要是有好的项目，肯定会去尝试一下的吧？！
原文: 啊，但是即便是有好项目你也不会投资的，因为你没钱，哈哈。
翻译: 아, 하지만 좋은 프로젝트가 있어도 투자하지 않으실 거예요, 돈이 없으시니까요, ㅋ.

将以下内容从中文翻译成韩文，使用敬语：
散步回来了。啊，天气真好呢。
2025-05-30 21:13:16,077 - DEBUG - 【构建提示词】长度: 1146 字符
2025-05-30 21:13:16,181 - DEBUG - API密钥解密成功
2025-05-30 21:13:16,181 - DEBUG - API密钥解密成功
2025-05-30 21:13:16,181 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-30 21:13:16,182 - DEBUG - 思考模式已禁用（未设置thinkingConfig参数 或预算为0）
2025-05-30 21:13:16,182 - DEBUG - 明确禁用思考模式(generationConfig.thinkingConfig.thinkingBudget=0)
2025-05-30 21:13:17,135 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "산책하고 돌아왔어요. 아, 날씨 정말 좋네요."
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 753,
    "candidatesTokenCount": 15,
    "totalTokenCount": 768,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 753
      }
    ]
  },
  "modelVersion": "models/gemini-2.5-flash-preview-04-17",
  "responseId": "3KA5aLyBO4bO_uMP5szFeA"
}

2025-05-30 21:13:17,135 - DEBUG - 【API响应JSON对象】模型: gemini-2.5-flash-preview-04-17, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '산책하고 돌아왔어요. 아, 날씨 정말 좋네요.'}], 'role': 'model'}, 'finishReason': 'STOP', 'index': 0}], 'usageMetadata': {'promptTokenCount': 753, 'candidatesTokenCount': 15, 'totalTokenCount': 768, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 753}]}, 'modelVersion': 'models/gemini-2.5-flash-preview-04-17', 'responseId': '3KA5aLyBO4bO_uMP5szFeA'}
2025-05-30 21:13:17,136 - DEBUG - 【Token使用情况】模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:13:17,136 - DEBUG -   - 思考Token数: 0
2025-05-30 21:13:17,137 - DEBUG -   - 提示Token数: 753
2025-05-30 21:13:17,137 - DEBUG -   - 输出Token数: 15
2025-05-30 21:13:17,138 - DEBUG -   - 总Token数: 768
2025-05-30 21:13:17,139 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 98, 3664.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:13:17,139 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.89)]
2025-05-30 21:13:17,140 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-30 21:13:17,140 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-30 21:13:17,140 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-30 21:13:17,140 - INFO - API翻译成功。模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:13:17,675 - DEBUG - 输入框内容已替换为: 산책하고 돌아왔어요. 아, 날씨 정말 좋네요.
2025-05-30 21:13:17,676 - INFO - 【翻译结果】
산책하고 돌아왔어요. 아, 날씨 정말 좋네요.
2025-05-30 21:13:17,677 - DEBUG - 缓存已满，移除最久未使用项: 2-zh-ko-389164381400...
2025-05-30 21:13:17,678 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-30 21:13:17,678 - DEBUG - 缓存已满，移除最久未使用项: 2-ko-zh-576240231293...
2025-05-30 21:13:17,678 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-30 21:13:17,679 - INFO - 翻译完成，结果已替换输入框内容
2025-05-30 21:13:17,681 - INFO - 已立即保存 2 条缓存记录
2025-05-30 21:13:17,684 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-30 21:14:37,829 - INFO - 使用脚本目录作为应用路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1
2025-05-30 21:14:38,050 - INFO - 语言模式配置已加载。
2025-05-30 21:14:38,050 - INFO - 语言模式配置已加载。
2025-05-30 21:14:38,050 - INFO - 【初始化】语言检测缓存，容量: 100
2025-05-30 21:14:38,050 - INFO - 【初始化】翻译缓存，容量: 50
2025-05-30 21:14:38,056 - INFO - 【初始化】本地翻译缓存管理器，数据库路径: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.0\translation_cache.db
2025-05-30 21:14:38,071 - INFO - 控制台线程已启动，准备进入循环。
2025-05-30 21:14:38,071 - INFO - 控制台循环开始，准备显示菜单。
2025-05-30 21:14:38,072 - INFO - 菜单已显示，等待用户输入。
2025-05-30 21:14:38,160 - INFO - 翻译程序已启动，按三次空格触发翻译，或通过控制台切换模式
2025-05-30 21:14:38,866 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-30 21:14:39,068 - INFO - API健康检查成功: API密钥有效，可以访问Gemini模型
2025-05-30 21:14:41,996 - INFO - 用户输入: 0
2025-05-30 21:14:46,910 - INFO - 主配置文件已保存: C:\Users\<USER>\Desktop\代码\翻译软件代码\多语言互译\2.1.1\config.yaml
2025-05-30 21:14:46,911 - INFO - 🔧 调试模式已开启 - 将显示详细的翻译信息
2025-05-30 21:14:48,448 - INFO - 控制台循环开始，准备显示菜单。
2025-05-30 21:14:48,451 - INFO - 菜单已显示，等待用户输入。
2025-05-30 21:14:57,370 - INFO - 检测到三次空格，触发翻译
2025-05-30 21:14:57,372 - INFO - 【原文】
바람 선선하고 좋죠 ㅎㅎㅎ

한강길 따라 산책하신거에요?
2025-05-30 21:14:57,374 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 98, 3718.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:14:57,375 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.88)]
2025-05-30 21:14:57,375 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-30 21:14:57,375 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-30 21:14:57,376 - INFO - 检测到原文语言: ko
2025-05-30 21:14:57,376 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-30 21:14:57,376 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-30 21:14:57,377 - DEBUG - 检测到输入语言 (ko) 与当前模式定义的目标语言 (ko) 一致，触发反向翻译流程。
2025-05-30 21:14:57,377 - DEBUG - 检测到语言: ko, 输入语言: 韩文(ko), 输出语言: 中文(zh)
2025-05-30 21:14:57,377 - DEBUG - 使用语气词 - 输入: [ㅋ|ㅎ|아|네|헤|ㅜ], 输出: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜]
2025-05-30 21:14:57,377 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-30 21:14:57,378 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


将以下内容从韩文翻译成中文：
바람 선선하고 좋죠 ㅎㅎㅎ 한강길 따라 산책하신거에요?
2025-05-30 21:14:57,378 - DEBUG - 【构建提示词】长度: 616 字符
2025-05-30 21:14:57,482 - DEBUG - API密钥解密成功
2025-05-30 21:14:57,482 - DEBUG - API密钥解密成功
2025-05-30 21:14:57,482 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-30 21:14:57,483 - DEBUG - 思考模式已禁用（未设置thinkingConfig参数 或预算为0）
2025-05-30 21:14:57,483 - DEBUG - 明确禁用思考模式(generationConfig.thinkingConfig.thinkingBudget=0)
2025-05-30 21:14:58,429 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "风很凉爽，感觉真好，嘿嘿嘿。是沿着汉江边散步了吗？"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 408,
    "candidatesTokenCount": 22,
    "totalTokenCount": 430,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 408
      }
    ]
  },
  "modelVersion": "models/gemini-2.5-flash-preview-04-17",
  "responseId": "QqE5aLC3ErS9_uMPpd-6gQ4"
}

2025-05-30 21:14:58,430 - DEBUG - 【API响应JSON对象】模型: gemini-2.5-flash-preview-04-17, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '风很凉爽，感觉真好，嘿嘿嘿。是沿着汉江边散步了吗？'}], 'role': 'model'}, 'finishReason': 'STOP', 'index': 0}], 'usageMetadata': {'promptTokenCount': 408, 'candidatesTokenCount': 22, 'totalTokenCount': 430, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 408}]}, 'modelVersion': 'models/gemini-2.5-flash-preview-04-17', 'responseId': 'QqE5aLC3ErS9_uMPpd-6gQ4'}
2025-05-30 21:14:58,430 - DEBUG - 【Token使用情况】模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:14:58,430 - DEBUG -   - 思考Token数: 0
2025-05-30 21:14:58,431 - DEBUG -   - 提示Token数: 408
2025-05-30 21:14:58,431 - DEBUG -   - 输出Token数: 22
2025-05-30 21:14:58,431 - DEBUG -   - 总Token数: 430
2025-05-30 21:14:58,432 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 98, 1879.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:14:58,432 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.938)]
2025-05-30 21:14:58,432 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-30 21:14:58,432 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-30 21:14:58,432 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-30 21:14:58,432 - INFO - API翻译成功。模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:14:58,964 - DEBUG - 输入框内容已替换为: 风很凉爽，感觉真好，嘿嘿嘿。是沿着汉江边散步了吗？
2025-05-30 21:14:58,965 - INFO - 【翻译结果】
风很凉爽，感觉真好，嘿嘿嘿。是沿着汉江边散步了吗？
2025-05-30 21:14:58,965 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-30 21:14:58,965 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-30 21:14:58,980 - INFO - 翻译完成，结果已替换输入框内容
2025-05-30 21:14:58,982 - INFO - 已立即保存 2 条缓存记录
2025-05-30 21:14:58,985 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-30 21:15:30,891 - INFO - 检测到三次空格，触发翻译
2025-05-30 21:15:30,892 - INFO - 【原文】
是的。我昨天让你准备的资料准备好了没有？身份证或者驾驶证或者护照。
2025-05-30 21:15:30,892 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 98, 2025.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:15:30,893 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.959)]
2025-05-30 21:15:30,893 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-30 21:15:30,893 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-30 21:15:30,893 - INFO - 检测到原文语言: zh
2025-05-30 21:15:30,893 - INFO - 执行正向翻译为: ko
2025-05-30 21:15:30,894 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-30 21:15:30,894 - DEBUG - 检测到语言: zh, 输入语言: 中文(zh), 输出语言: 韩文(ko)
2025-05-30 21:15:30,894 - DEBUG - 使用语气词 - 输入: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜], 输出: [ㅋ|ㅎ|아|네|헤|ㅜ]
2025-05-30 21:15:30,894 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-30 21:15:30,894 - INFO - 模式 2 当前上下文数量: 1（最大: 8）
2025-05-30 21:15:30,894 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 中文 和输出为 韩文 ，将内容从 中文 翻译成 韩文 ，使用敬语
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 韩文，调整语气和风格，考虑文化内涵和地区差异。不得包含 中文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 中的语气助词时，才将其翻译为 韩文 中等效的 [ㅋ|ㅎ|아|네|헤|ㅜ] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 韩文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 韩文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: 바람 선선하고 좋죠 ㅎㅎㅎ

한강길 따라 산책하신거에요?
翻译: 风很凉爽，感觉真好，嘿嘿嘿。是沿着汉江边散步了吗？

将以下内容从中文翻译成韩文，使用敬语：
是的。我昨天让你准备的资料准备好了没有？身份证或者驾驶证或者护照。
2025-05-30 21:15:30,895 - DEBUG - 【构建提示词】长度: 722 字符
2025-05-30 21:15:31,087 - DEBUG - API密钥解密成功
2025-05-30 21:15:31,088 - DEBUG - API密钥解密成功
2025-05-30 21:15:31,088 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-30 21:15:31,089 - DEBUG - 思考模式已禁用（未设置thinkingConfig参数 或预算为0）
2025-05-30 21:15:31,089 - DEBUG - 明确禁用思考模式(generationConfig.thinkingConfig.thinkingBudget=0)
2025-05-30 21:15:31,935 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "네. 제가 어제 준비해 달라고 했던 자료는 준비되셨나요? 신분증이나 운전면허증, 아니면 여권이요."
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 479,
    "candidatesTokenCount": 34,
    "totalTokenCount": 513,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 479
      }
    ]
  },
  "modelVersion": "models/gemini-2.5-flash-preview-04-17",
  "responseId": "Y6E5aJGENInj_uMPopbPwAo"
}

2025-05-30 21:15:31,935 - DEBUG - 【API响应JSON对象】模型: gemini-2.5-flash-preview-04-17, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '네. 제가 어제 준비해 달라고 했던 자료는 준비되셨나요? 신분증이나 운전면허증, 아니면 여권이요.'}], 'role': 'model'}, 'finishReason': 'STOP', 'index': 0}], 'usageMetadata': {'promptTokenCount': 479, 'candidatesTokenCount': 34, 'totalTokenCount': 513, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 479}]}, 'modelVersion': 'models/gemini-2.5-flash-preview-04-17', 'responseId': 'Y6E5aJGENInj_uMPopbPwAo'}
2025-05-30 21:15:31,935 - DEBUG - 【Token使用情况】模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:15:31,936 - DEBUG -   - 思考Token数: 0
2025-05-30 21:15:31,936 - DEBUG -   - 提示Token数: 479
2025-05-30 21:15:31,936 - DEBUG -   - 输出Token数: 34
2025-05-30 21:15:31,936 - DEBUG -   - 总Token数: 513
2025-05-30 21:15:31,936 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 99, 3714.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:15:31,937 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.91)]
2025-05-30 21:15:31,937 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-30 21:15:31,937 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-30 21:15:31,937 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-30 21:15:31,937 - INFO - API翻译成功。模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:15:32,477 - DEBUG - 输入框内容已替换为: 네. 제가 어제 준비해 달라고 했던 자료는 준비되셨나요? 신분증이나 운전면허증, 아니면 여권이요.
2025-05-30 21:15:32,478 - INFO - 【翻译结果】
네. 제가 어제 준비해 달라고 했던 자료는 준비되셨나요? 신분증이나 운전면허증, 아니면 여권이요.
2025-05-30 21:15:32,479 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-30 21:15:32,480 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-30 21:15:32,481 - INFO - 翻译完成，结果已替换输入框内容
2025-05-30 21:15:32,483 - INFO - 已立即保存 2 条缓存记录
2025-05-30 21:15:32,486 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-30 21:18:03,901 - INFO - 检测到三次空格，触发翻译
2025-05-30 21:18:03,902 - INFO - 【原文】
ㅎㅎㅎㅎ 전 이제 퇴근해서 ㅎㅎ
2025-05-30 21:18:03,903 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 97, 3630.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:18:03,903 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.803)]
2025-05-30 21:18:03,904 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-30 21:18:03,904 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-30 21:18:03,904 - INFO - 检测到原文语言: ko
2025-05-30 21:18:03,904 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-30 21:18:03,904 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-30 21:18:03,904 - DEBUG - 检测到输入语言 (ko) 与当前模式定义的目标语言 (ko) 一致，触发反向翻译流程。
2025-05-30 21:18:03,904 - DEBUG - 检测到语言: ko, 输入语言: 韩文(ko), 输出语言: 中文(zh)
2025-05-30 21:18:03,905 - DEBUG - 使用语气词 - 输入: [ㅋ|ㅎ|아|네|헤|ㅜ], 输出: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜]
2025-05-30 21:18:03,905 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-30 21:18:03,905 - INFO - 模式 2 当前上下文数量: 2（最大: 8）
2025-05-30 21:18:03,905 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: 바람 선선하고 좋죠 ㅎㅎㅎ

한강길 따라 산책하신거에요?
翻译: 风很凉爽，感觉真好，嘿嘿嘿。是沿着汉江边散步了吗？
原文: 是的。我昨天让你准备的资料准备好了没有？身份证或者驾驶证或者护照。
翻译: 네. 제가 어제 준비해 달라고 했던 자료는 준비되셨나요? 신분증이나 운전면허증, 아니면 여권이요.

将以下内容从韩文翻译成中文：
ㅎㅎㅎㅎ 전 이제 퇴근해서 ㅎㅎ
2025-05-30 21:18:03,905 - DEBUG - 【构建提示词】长度: 793 字符
2025-05-30 21:18:04,119 - DEBUG - API密钥解密成功
2025-05-30 21:18:04,120 - DEBUG - API密钥解密成功
2025-05-30 21:18:04,120 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-30 21:18:04,121 - DEBUG - 思考模式已禁用（未设置thinkingConfig参数 或预算为0）
2025-05-30 21:18:04,121 - DEBUG - 明确禁用思考模式(generationConfig.thinkingConfig.thinkingBudget=0)
2025-05-30 21:18:05,058 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "哈哈哈哈，我刚下班，哈哈。"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 521,
    "candidatesTokenCount": 10,
    "totalTokenCount": 531,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 521
      }
    ]
  },
  "modelVersion": "models/gemini-2.5-flash-preview-04-17",
  "responseId": "_KE5aNusOaHwjrEPuo7liAg"
}

2025-05-30 21:18:05,059 - DEBUG - 【API响应JSON对象】模型: gemini-2.5-flash-preview-04-17, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '哈哈哈哈，我刚下班，哈哈。'}], 'role': 'model'}, 'finishReason': 'STOP', 'index': 0}], 'usageMetadata': {'promptTokenCount': 521, 'candidatesTokenCount': 10, 'totalTokenCount': 531, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 521}]}, 'modelVersion': 'models/gemini-2.5-flash-preview-04-17', 'responseId': '_KE5aNusOaHwjrEPuo7liAg'}
2025-05-30 21:18:05,059 - DEBUG - 【Token使用情况】模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:18:05,059 - DEBUG -   - 思考Token数: 0
2025-05-30 21:18:05,060 - DEBUG -   - 提示Token数: 521
2025-05-30 21:18:05,060 - DEBUG -   - 输出Token数: 10
2025-05-30 21:18:05,060 - DEBUG -   - 总Token数: 531
2025-05-30 21:18:05,060 - DEBUG - pycld2 检测结果: is_reliable=False, details=(('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:18:05,061 - DEBUG - 基于特征补充候选: zh (score: 0.3846)
2025-05-30 21:18:05,061 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.385)]
2025-05-30 21:18:05,061 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-30 21:18:05,061 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-30 21:18:05,061 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-30 21:18:05,061 - INFO - API翻译成功。模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:18:05,587 - DEBUG - 输入框内容已替换为: 哈哈哈哈，我刚下班，哈哈。
2025-05-30 21:18:05,588 - INFO - 【翻译结果】
哈哈哈哈，我刚下班，哈哈。
2025-05-30 21:18:05,589 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-30 21:18:05,589 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-30 21:18:05,590 - INFO - 翻译完成，结果已替换输入框内容
2025-05-30 21:18:05,591 - INFO - 已立即保存 2 条缓存记录
2025-05-30 21:18:05,595 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-30 21:18:33,750 - INFO - 检测到三次空格，触发翻译
2025-05-30 21:18:33,751 - INFO - 【原文】
啊。真辛苦呢。每天你准备一下身份证或者护照或者驾驶证吧，我带你注册交易所。
2025-05-30 21:18:33,752 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 99, 1948.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:18:33,752 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.961)]
2025-05-30 21:18:33,753 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-30 21:18:33,753 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-30 21:18:33,753 - INFO - 检测到原文语言: zh
2025-05-30 21:18:33,753 - INFO - 执行正向翻译为: ko
2025-05-30 21:18:33,754 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-30 21:18:33,754 - DEBUG - 检测到语言: zh, 输入语言: 中文(zh), 输出语言: 韩文(ko)
2025-05-30 21:18:33,754 - DEBUG - 使用语气词 - 输入: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜], 输出: [ㅋ|ㅎ|아|네|헤|ㅜ]
2025-05-30 21:18:33,754 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-30 21:18:33,754 - INFO - 模式 2 当前上下文数量: 3（最大: 8）
2025-05-30 21:18:33,755 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 中文 和输出为 韩文 ，将内容从 中文 翻译成 韩文 ，使用敬语
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 韩文，调整语气和风格，考虑文化内涵和地区差异。不得包含 中文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 中的语气助词时，才将其翻译为 韩文 中等效的 [ㅋ|ㅎ|아|네|헤|ㅜ] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 韩文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 韩文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: 바람 선선하고 좋죠 ㅎㅎㅎ

한강길 따라 산책하신거에요?
翻译: 风很凉爽，感觉真好，嘿嘿嘿。是沿着汉江边散步了吗？
原文: 是的。我昨天让你准备的资料准备好了没有？身份证或者驾驶证或者护照。
翻译: 네. 제가 어제 준비해 달라고 했던 자료는 준비되셨나요? 신분증이나 운전면허증, 아니면 여권이요.
原文: ㅎㅎㅎㅎ 전 이제 퇴근해서 ㅎㅎ
翻译: 哈哈哈哈，我刚下班，哈哈。

将以下内容从中文翻译成韩文，使用敬语：
啊。真辛苦呢。每天你准备一下身份证或者护照或者驾驶证吧，我带你注册交易所。
2025-05-30 21:18:33,755 - DEBUG - 【构建提示词】长度: 863 字符
2025-05-30 21:18:33,860 - DEBUG - API密钥解密成功
2025-05-30 21:18:33,861 - DEBUG - API密钥解密成功
2025-05-30 21:18:33,861 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-30 21:18:33,861 - DEBUG - 思考模式已禁用（未设置thinkingConfig参数 或预算为0）
2025-05-30 21:18:33,862 - DEBUG - 明确禁用思考模式(generationConfig.thinkingConfig.thinkingBudget=0)
2025-05-30 21:18:34,737 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "아. 정말 수고 많으셨네요. 매일 신분증이나 여권, 아니면 운전면허증을 준비해 주세요. 제가 거래소 등록을 도와드릴게요."
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 570,
    "candidatesTokenCount": 40,
    "totalTokenCount": 610,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 570
      }
    ]
  },
  "modelVersion": "models/gemini-2.5-flash-preview-04-17",
  "responseId": "GqI5aLXLJsWH-8YPueTm-A0"
}

2025-05-30 21:18:34,737 - DEBUG - 【API响应JSON对象】模型: gemini-2.5-flash-preview-04-17, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '아. 정말 수고 많으셨네요. 매일 신분증이나 여권, 아니면 운전면허증을 준비해 주세요. 제가 거래소 등록을 도와드릴게요.'}], 'role': 'model'}, 'finishReason': 'STOP', 'index': 0}], 'usageMetadata': {'promptTokenCount': 570, 'candidatesTokenCount': 40, 'totalTokenCount': 610, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 570}]}, 'modelVersion': 'models/gemini-2.5-flash-preview-04-17', 'responseId': 'GqI5aLXLJsWH-8YPueTm-A0'}
2025-05-30 21:18:34,738 - DEBUG - 【Token使用情况】模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:18:34,738 - DEBUG -   - 思考Token数: 0
2025-05-30 21:18:34,739 - DEBUG -   - 提示Token数: 570
2025-05-30 21:18:34,739 - DEBUG -   - 输出Token数: 40
2025-05-30 21:18:34,740 - DEBUG -   - 总Token数: 610
2025-05-30 21:18:34,740 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 99, 3709.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:18:34,741 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.908)]
2025-05-30 21:18:34,741 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-30 21:18:34,742 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-30 21:18:34,742 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-30 21:18:34,742 - INFO - API翻译成功。模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:18:35,286 - DEBUG - 输入框内容已替换为: 아. 정말 수고 많으셨네요. 매일 신분증이나 여권, 아니면 운전면허증을 준비해 주세요. 제가 거래소 등록을 도와드릴게요.
2025-05-30 21:18:35,288 - INFO - 【翻译结果】
아. 정말 수고 많으셨네요. 매일 신분증이나 여권, 아니면 운전면허증을 준비해 주세요. 제가 거래소 등록을 도와드릴게요.
2025-05-30 21:18:35,288 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-30 21:18:35,288 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-30 21:18:35,289 - INFO - 翻译完成，结果已替换输入框内容
2025-05-30 21:18:35,291 - INFO - 已立即保存 2 条缓存记录
2025-05-30 21:18:35,294 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-30 21:18:44,238 - INFO - 检测到三次空格，触发翻译
2025-05-30 21:18:44,240 - INFO - 【原文】
네 엄마 다 할터주고십어
2025-05-30 21:18:44,240 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 97, 3614.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:18:44,241 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.91)]
2025-05-30 21:18:44,241 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-30 21:18:44,241 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-30 21:18:44,241 - INFO - 检测到原文语言: ko
2025-05-30 21:18:44,242 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-30 21:18:44,242 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-30 21:18:44,242 - DEBUG - 检测到输入语言 (ko) 与当前模式定义的目标语言 (ko) 一致，触发反向翻译流程。
2025-05-30 21:18:44,242 - DEBUG - 检测到语言: ko, 输入语言: 韩文(ko), 输出语言: 中文(zh)
2025-05-30 21:18:44,242 - DEBUG - 使用语气词 - 输入: [ㅋ|ㅎ|아|네|헤|ㅜ], 输出: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜]
2025-05-30 21:18:44,242 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-30 21:18:44,242 - INFO - 模式 2 当前上下文数量: 4（最大: 8）
2025-05-30 21:18:44,243 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: 바람 선선하고 좋죠 ㅎㅎㅎ

한강길 따라 산책하신거에요?
翻译: 风很凉爽，感觉真好，嘿嘿嘿。是沿着汉江边散步了吗？
原文: 是的。我昨天让你准备的资料准备好了没有？身份证或者驾驶证或者护照。
翻译: 네. 제가 어제 준비해 달라고 했던 자료는 준비되셨나요? 신분증이나 운전면허증, 아니면 여권이요.
原文: ㅎㅎㅎㅎ 전 이제 퇴근해서 ㅎㅎ
翻译: 哈哈哈哈，我刚下班，哈哈。
原文: 啊。真辛苦呢。每天你准备一下身份证或者护照或者驾驶证吧，我带你注册交易所。
翻译: 아. 정말 수고 많으셨네요. 매일 신분증이나 여권, 아니면 운전면허증을 준비해 주세요. 제가 거래소 등록을 도와드릴게요.

将以下内容从韩文翻译成中文：
네 엄마 다 할터주고십어
2025-05-30 21:18:44,243 - DEBUG - 【构建提示词】长度: 943 字符
2025-05-30 21:18:44,446 - DEBUG - API密钥解密成功
2025-05-30 21:18:44,446 - DEBUG - API密钥解密成功
2025-05-30 21:18:44,446 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-30 21:18:44,447 - DEBUG - 思考模式已禁用（未设置thinkingConfig参数 或预算为0）
2025-05-30 21:18:44,447 - DEBUG - 明确禁用思考模式(generationConfig.thinkingConfig.thinkingBudget=0)
2025-05-30 21:18:46,330 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "是的，妈妈，我想把一切都给你。"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 617,
    "candidatesTokenCount": 10,
    "totalTokenCount": 627,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 617
      }
    ]
  },
  "modelVersion": "models/gemini-2.5-flash-preview-04-17",
  "responseId": "JqI5aKWkDP2S-8YPvJWKiQc"
}

2025-05-30 21:18:46,330 - DEBUG - 【API响应JSON对象】模型: gemini-2.5-flash-preview-04-17, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '是的，妈妈，我想把一切都给你。'}], 'role': 'model'}, 'finishReason': 'STOP', 'index': 0}], 'usageMetadata': {'promptTokenCount': 617, 'candidatesTokenCount': 10, 'totalTokenCount': 627, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 617}]}, 'modelVersion': 'models/gemini-2.5-flash-preview-04-17', 'responseId': 'JqI5aKWkDP2S-8YPvJWKiQc'}
2025-05-30 21:18:46,330 - DEBUG - 【Token使用情况】模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:18:46,331 - DEBUG -   - 思考Token数: 0
2025-05-30 21:18:46,331 - DEBUG -   - 提示Token数: 617
2025-05-30 21:18:46,331 - DEBUG -   - 输出Token数: 10
2025-05-30 21:18:46,332 - DEBUG -   - 总Token数: 627
2025-05-30 21:18:46,333 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 97, 1864.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:18:46,333 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.919)]
2025-05-30 21:18:46,333 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-30 21:18:46,333 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-30 21:18:46,333 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-30 21:18:46,334 - INFO - API翻译成功。模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:18:46,863 - DEBUG - 输入框内容已替换为: 是的，妈妈，我想把一切都给你。
2025-05-30 21:18:46,864 - INFO - 【翻译结果】
是的，妈妈，我想把一切都给你。
2025-05-30 21:18:46,865 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-30 21:18:46,865 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-30 21:18:46,866 - INFO - 翻译完成，结果已替换输入框内容
2025-05-30 21:18:46,868 - INFO - 已立即保存 2 条缓存记录
2025-05-30 21:18:46,871 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-30 21:20:01,496 - INFO - 检测到三次空格，触发翻译
2025-05-30 21:20:01,496 - INFO - 【原文】
既然你如此顺从了我，那就先从最容易的开始证明。好狗狗，把你的照片发给妈妈看看~
2025-05-30 21:20:01,497 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 99, 1954.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:20:01,497 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.962)]
2025-05-30 21:20:01,498 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-30 21:20:01,498 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-30 21:20:01,498 - INFO - 检测到原文语言: zh
2025-05-30 21:20:01,498 - INFO - 执行正向翻译为: ko
2025-05-30 21:20:01,498 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-30 21:20:01,498 - DEBUG - 检测到语言: zh, 输入语言: 中文(zh), 输出语言: 韩文(ko)
2025-05-30 21:20:01,498 - DEBUG - 使用语气词 - 输入: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜], 输出: [ㅋ|ㅎ|아|네|헤|ㅜ]
2025-05-30 21:20:01,499 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-30 21:20:01,499 - INFO - 模式 2 当前上下文数量: 5（最大: 8）
2025-05-30 21:20:01,499 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 中文 和输出为 韩文 ，将内容从 中文 翻译成 韩文 ，使用敬语
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 韩文，调整语气和风格，考虑文化内涵和地区差异。不得包含 中文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 中的语气助词时，才将其翻译为 韩文 中等效的 [ㅋ|ㅎ|아|네|헤|ㅜ] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 韩文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 韩文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: 바람 선선하고 좋죠 ㅎㅎㅎ

한강길 따라 산책하신거에요?
翻译: 风很凉爽，感觉真好，嘿嘿嘿。是沿着汉江边散步了吗？
原文: 是的。我昨天让你准备的资料准备好了没有？身份证或者驾驶证或者护照。
翻译: 네. 제가 어제 준비해 달라고 했던 자료는 준비되셨나요? 신분증이나 운전면허증, 아니면 여권이요.
原文: ㅎㅎㅎㅎ 전 이제 퇴근해서 ㅎㅎ
翻译: 哈哈哈哈，我刚下班，哈哈。
原文: 啊。真辛苦呢。每天你准备一下身份证或者护照或者驾驶证吧，我带你注册交易所。
翻译: 아. 정말 수고 많으셨네요. 매일 신분증이나 여권, 아니면 운전면허증을 준비해 주세요. 제가 거래소 등록을 도와드릴게요.
原文: 네 엄마 다 할터주고십어
翻译: 是的，妈妈，我想把一切都给你。

将以下内容从中文翻译成韩文，使用敬语：
既然你如此顺从了我，那就先从最容易的开始证明。好狗狗，把你的照片发给妈妈看看~
2025-05-30 21:20:01,499 - DEBUG - 【构建提示词】长度: 1017 字符
2025-05-30 21:20:02,082 - DEBUG - API密钥解密成功
2025-05-30 21:20:02,083 - DEBUG - API密钥解密成功
2025-05-30 21:20:02,083 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-30 21:20:02,083 - DEBUG - 思考模式已禁用（未设置thinkingConfig参数 或预算为0）
2025-05-30 21:20:02,083 - DEBUG - 明确禁用思考模式(generationConfig.thinkingConfig.thinkingBudget=0)
2025-05-30 21:20:03,274 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "이렇게 엄마에게 순종하는 모습을 보여주니, 가장 쉬운 것부터 증명해 보자꾸나. 착한 강아지, 네 사진을 엄마에게 보내주렴~"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 668,
    "candidatesTokenCount": 38,
    "totalTokenCount": 706,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 668
      }
    ]
  },
  "modelVersion": "models/gemini-2.5-flash-preview-04-17",
  "responseId": "c6I5aOnGCtCc-8YP8qL18AE"
}

2025-05-30 21:20:03,275 - DEBUG - 【API响应JSON对象】模型: gemini-2.5-flash-preview-04-17, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '이렇게 엄마에게 순종하는 모습을 보여주니, 가장 쉬운 것부터 증명해 보자꾸나. 착한 강아지, 네 사진을 엄마에게 보내주렴~'}], 'role': 'model'}, 'finishReason': 'STOP', 'index': 0}], 'usageMetadata': {'promptTokenCount': 668, 'candidatesTokenCount': 38, 'totalTokenCount': 706, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 668}]}, 'modelVersion': 'models/gemini-2.5-flash-preview-04-17', 'responseId': 'c6I5aOnGCtCc-8YP8qL18AE'}
2025-05-30 21:20:03,275 - DEBUG - 【Token使用情况】模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:20:03,276 - DEBUG -   - 思考Token数: 0
2025-05-30 21:20:03,276 - DEBUG -   - 提示Token数: 668
2025-05-30 21:20:03,276 - DEBUG -   - 输出Token数: 38
2025-05-30 21:20:03,277 - DEBUG -   - 总Token数: 706
2025-05-30 21:20:03,278 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 99, 3693.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:20:03,278 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.909)]
2025-05-30 21:20:03,279 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-30 21:20:03,279 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-30 21:20:03,279 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-30 21:20:03,280 - INFO - API翻译成功。模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:20:03,819 - DEBUG - 输入框内容已替换为: 이렇게 엄마에게 순종하는 모습을 보여주니, 가장 쉬운 것부터 증명해 보자꾸나. 착한 강아지, 네 사진을 엄마에게 보내주렴~
2025-05-30 21:20:03,821 - INFO - 【翻译结果】
이렇게 엄마에게 순종하는 모습을 보여주니, 가장 쉬운 것부터 증명해 보자꾸나. 착한 강아지, 네 사진을 엄마에게 보내주렴~
2025-05-30 21:20:03,821 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-30 21:20:03,821 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-30 21:20:03,822 - INFO - 翻译完成，结果已替换输入框内容
2025-05-30 21:20:03,824 - INFO - 已立即保存 2 条缓存记录
2025-05-30 21:20:03,827 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-30 21:20:35,344 - INFO - 检测到三次空格，触发翻译
2025-05-30 21:20:35,345 - INFO - 【原文】
그건 항상있죠
2025-05-30 21:20:35,347 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 95, 3686.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:20:35,347 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.894)]
2025-05-30 21:20:35,348 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-30 21:20:35,348 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-30 21:20:35,348 - INFO - 检测到原文语言: ko
2025-05-30 21:20:35,348 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-30 21:20:35,348 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-30 21:20:35,349 - DEBUG - 检测到输入语言 (ko) 与当前模式定义的目标语言 (ko) 一致，触发反向翻译流程。
2025-05-30 21:20:35,349 - DEBUG - 检测到语言: ko, 输入语言: 韩文(ko), 输出语言: 中文(zh)
2025-05-30 21:20:35,349 - DEBUG - 使用语气词 - 输入: [ㅋ|ㅎ|아|네|헤|ㅜ], 输出: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜]
2025-05-30 21:20:35,349 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-30 21:20:35,349 - INFO - 模式 2 当前上下文数量: 6（最大: 8）
2025-05-30 21:20:35,349 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: 바람 선선하고 좋죠 ㅎㅎㅎ

한강길 따라 산책하신거에요?
翻译: 风很凉爽，感觉真好，嘿嘿嘿。是沿着汉江边散步了吗？
原文: 是的。我昨天让你准备的资料准备好了没有？身份证或者驾驶证或者护照。
翻译: 네. 제가 어제 준비해 달라고 했던 자료는 준비되셨나요? 신분증이나 운전면허증, 아니면 여권이요.
原文: ㅎㅎㅎㅎ 전 이제 퇴근해서 ㅎㅎ
翻译: 哈哈哈哈，我刚下班，哈哈。
原文: 啊。真辛苦呢。每天你准备一下身份证或者护照或者驾驶证吧，我带你注册交易所。
翻译: 아. 정말 수고 많으셨네요. 매일 신분증이나 여권, 아니면 운전면허증을 준비해 주세요. 제가 거래소 등록을 도와드릴게요.
原文: 네 엄마 다 할터주고십어
翻译: 是的，妈妈，我想把一切都给你。
原文: 既然你如此顺从了我，那就先从最容易的开始证明。好狗狗，把你的照片发给妈妈看看~
翻译: 이렇게 엄마에게 순종하는 모습을 보여주니, 가장 쉬운 것부터 증명해 보자꾸나. 착한 강아지, 네 사진을 엄마에게 보내주렴~

将以下内容从韩文翻译成中文：
그건 항상있죠
2025-05-30 21:20:35,349 - DEBUG - 【构建提示词】长度: 1092 字符
2025-05-30 21:20:35,559 - DEBUG - API密钥解密成功
2025-05-30 21:20:35,559 - DEBUG - API密钥解密成功
2025-05-30 21:20:35,559 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-30 21:20:35,560 - DEBUG - 思考模式已禁用（未设置thinkingConfig参数 或预算为0）
2025-05-30 21:20:35,560 - DEBUG - 明确禁用思考模式(generationConfig.thinkingConfig.thinkingBudget=0)
2025-05-30 21:20:36,502 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "那个一直都有。"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 711,
    "candidatesTokenCount": 4,
    "totalTokenCount": 715,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 711
      }
    ]
  },
  "modelVersion": "models/gemini-2.5-flash-preview-04-17",
  "responseId": "lKI5aICYF72Q-8YPrd-q0QY"
}

2025-05-30 21:20:36,502 - DEBUG - 【API响应JSON对象】模型: gemini-2.5-flash-preview-04-17, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '那个一直都有。'}], 'role': 'model'}, 'finishReason': 'STOP', 'index': 0}], 'usageMetadata': {'promptTokenCount': 711, 'candidatesTokenCount': 4, 'totalTokenCount': 715, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 711}]}, 'modelVersion': 'models/gemini-2.5-flash-preview-04-17', 'responseId': 'lKI5aICYF72Q-8YPrd-q0QY'}
2025-05-30 21:20:36,503 - DEBUG - 【Token使用情况】模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:20:36,503 - DEBUG -   - 思考Token数: 0
2025-05-30 21:20:36,503 - DEBUG -   - 提示Token数: 711
2025-05-30 21:20:36,503 - DEBUG -   - 输出Token数: 4
2025-05-30 21:20:36,504 - DEBUG -   - 总Token数: 715
2025-05-30 21:20:36,505 - DEBUG - pycld2 检测结果: is_reliable=False, details=(('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:20:36,505 - DEBUG - 基于特征补充候选: zh (score: 0.4286)
2025-05-30 21:20:36,505 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.429)]
2025-05-30 21:20:36,506 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-30 21:20:36,506 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-30 21:20:36,506 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-30 21:20:36,506 - INFO - API翻译成功。模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:20:37,043 - DEBUG - 输入框内容已替换为: 那个一直都有。
2025-05-30 21:20:37,044 - INFO - 【翻译结果】
那个一直都有。
2025-05-30 21:20:37,044 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-30 21:20:37,045 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-30 21:20:37,046 - INFO - 翻译完成，结果已替换输入框内容
2025-05-30 21:20:37,047 - INFO - 已立即保存 2 条缓存记录
2025-05-30 21:20:37,051 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-30 21:20:50,585 - INFO - 检测到三次空格，触发翻译
2025-05-30 21:20:50,586 - INFO - 【原文】
啊。只需要照片就可以了。不用原件。
2025-05-30 21:20:50,587 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('ChineseT', 'zh-Hant', 97, 1865.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:20:50,587 - DEBUG - 基于特征补充候选: zh (score: 0.4118)
2025-05-30 21:20:50,588 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.412)]
2025-05-30 21:20:50,588 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-30 21:20:50,588 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-30 21:20:50,588 - INFO - 检测到原文语言: zh
2025-05-30 21:20:50,588 - INFO - 执行正向翻译为: ko
2025-05-30 21:20:50,588 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-30 21:20:50,589 - DEBUG - 检测到语言: zh, 输入语言: 中文(zh), 输出语言: 韩文(ko)
2025-05-30 21:20:50,589 - DEBUG - 使用语气词 - 输入: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜], 输出: [ㅋ|ㅎ|아|네|헤|ㅜ]
2025-05-30 21:20:50,589 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-30 21:20:50,589 - INFO - 模式 2 当前上下文数量: 7（最大: 8）
2025-05-30 21:20:50,589 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 中文 和输出为 韩文 ，将内容从 中文 翻译成 韩文 ，使用敬语
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 韩文，调整语气和风格，考虑文化内涵和地区差异。不得包含 中文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 中的语气助词时，才将其翻译为 韩文 中等效的 [ㅋ|ㅎ|아|네|헤|ㅜ] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 韩文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 韩文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: 바람 선선하고 좋죠 ㅎㅎㅎ

한강길 따라 산책하신거에요?
翻译: 风很凉爽，感觉真好，嘿嘿嘿。是沿着汉江边散步了吗？
原文: 是的。我昨天让你准备的资料准备好了没有？身份证或者驾驶证或者护照。
翻译: 네. 제가 어제 준비해 달라고 했던 자료는 준비되셨나요? 신분증이나 운전면허증, 아니면 여권이요.
原文: ㅎㅎㅎㅎ 전 이제 퇴근해서 ㅎㅎ
翻译: 哈哈哈哈，我刚下班，哈哈。
原文: 啊。真辛苦呢。每天你准备一下身份证或者护照或者驾驶证吧，我带你注册交易所。
翻译: 아. 정말 수고 많으셨네요. 매일 신분증이나 여권, 아니면 운전면허증을 준비해 주세요. 제가 거래소 등록을 도와드릴게요.
原文: 네 엄마 다 할터주고십어
翻译: 是的，妈妈，我想把一切都给你。
原文: 既然你如此顺从了我，那就先从最容易的开始证明。好狗狗，把你的照片发给妈妈看看~
翻译: 이렇게 엄마에게 순종하는 모습을 보여주니, 가장 쉬운 것부터 증명해 보자꾸나. 착한 강아지, 네 사진을 엄마에게 보내주렴~
原文: 그건 항상있죠
翻译: 那个一直都有。

将以下内容从中文翻译成韩文，使用敬语：
啊。只需要照片就可以了。不用原件。
2025-05-30 21:20:50,589 - DEBUG - 【构建提示词】长度: 1136 字符
2025-05-30 21:20:50,692 - DEBUG - API密钥解密成功
2025-05-30 21:20:50,692 - DEBUG - API密钥解密成功
2025-05-30 21:20:50,693 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-30 21:20:50,693 - DEBUG - 思考模式已禁用（未设置thinkingConfig参数 或预算为0）
2025-05-30 21:20:50,693 - DEBUG - 明确禁用思考模式(generationConfig.thinkingConfig.thinkingBudget=0)
2025-05-30 21:20:51,489 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "아. 사진만 있으면 돼요. 원본은 필요 없어요."
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 739,
    "candidatesTokenCount": 14,
    "totalTokenCount": 753,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 739
      }
    ]
  },
  "modelVersion": "models/gemini-2.5-flash-preview-04-17",
  "responseId": "o6I5aI-zGKaf-8YP1aLH4Qk"
}

2025-05-30 21:20:51,490 - DEBUG - 【API响应JSON对象】模型: gemini-2.5-flash-preview-04-17, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '아. 사진만 있으면 돼요. 원본은 필요 없어요.'}], 'role': 'model'}, 'finishReason': 'STOP', 'index': 0}], 'usageMetadata': {'promptTokenCount': 739, 'candidatesTokenCount': 14, 'totalTokenCount': 753, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 739}]}, 'modelVersion': 'models/gemini-2.5-flash-preview-04-17', 'responseId': 'o6I5aI-zGKaf-8YP1aLH4Qk'}
2025-05-30 21:20:51,490 - DEBUG - 【Token使用情况】模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:20:51,491 - DEBUG -   - 思考Token数: 0
2025-05-30 21:20:51,491 - DEBUG -   - 提示Token数: 739
2025-05-30 21:20:51,491 - DEBUG -   - 输出Token数: 14
2025-05-30 21:20:51,491 - DEBUG -   - 总Token数: 753
2025-05-30 21:20:51,492 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 98, 3601.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:20:51,493 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.882)]
2025-05-30 21:20:51,493 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-30 21:20:51,493 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-30 21:20:51,493 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-30 21:20:51,494 - INFO - API翻译成功。模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:20:52,013 - DEBUG - 输入框内容已替换为: 아. 사진만 있으면 돼요. 원본은 필요 없어요.
2025-05-30 21:20:52,014 - INFO - 【翻译结果】
아. 사진만 있으면 돼요. 원본은 필요 없어요.
2025-05-30 21:20:52,014 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-30 21:20:52,014 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-30 21:20:52,015 - INFO - 翻译完成，结果已替换输入框内容
2025-05-30 21:20:52,017 - INFO - 已立即保存 2 条缓存记录
2025-05-30 21:20:52,020 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-30 21:21:05,457 - INFO - 检测到三次空格，触发翻译
2025-05-30 21:21:05,458 - INFO - 【原文】
네 3일에 투표해요
2025-05-30 21:21:05,458 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 96, 3584.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:21:05,459 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.882)]
2025-05-30 21:21:05,459 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-30 21:21:05,459 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-30 21:21:05,460 - INFO - 检测到原文语言: ko
2025-05-30 21:21:05,460 - INFO - 检测到目标语言文本，执行反向翻译为: zh
2025-05-30 21:21:05,460 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-30 21:21:05,460 - DEBUG - 检测到输入语言 (ko) 与当前模式定义的目标语言 (ko) 一致，触发反向翻译流程。
2025-05-30 21:21:05,460 - DEBUG - 检测到语言: ko, 输入语言: 韩文(ko), 输出语言: 中文(zh)
2025-05-30 21:21:05,460 - DEBUG - 使用语气词 - 输入: [ㅋ|ㅎ|아|네|헤|ㅜ], 输出: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜]
2025-05-30 21:21:05,461 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-30 21:21:05,461 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-30 21:21:05,461 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 韩文 和输出为 中文 ，将内容从 韩文 翻译成 中文 
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 中文，调整语气和风格，考虑文化内涵和地区差异。不得包含 韩文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [ㅋ|ㅎ|아|네|헤|ㅜ] 中的语气助词时，才将其翻译为 中文 中等效的 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 中文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 中文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: 바람 선선하고 좋죠 ㅎㅎㅎ

한강길 따라 산책하신거에요?
翻译: 风很凉爽，感觉真好，嘿嘿嘿。是沿着汉江边散步了吗？
原文: 是的。我昨天让你准备的资料准备好了没有？身份证或者驾驶证或者护照。
翻译: 네. 제가 어제 준비해 달라고 했던 자료는 준비되셨나요? 신분증이나 운전면허증, 아니면 여권이요.
原文: ㅎㅎㅎㅎ 전 이제 퇴근해서 ㅎㅎ
翻译: 哈哈哈哈，我刚下班，哈哈。
原文: 啊。真辛苦呢。每天你准备一下身份证或者护照或者驾驶证吧，我带你注册交易所。
翻译: 아. 정말 수고 많으셨네요. 매일 신분증이나 여권, 아니면 운전면허증을 준비해 주세요. 제가 거래소 등록을 도와드릴게요.
原文: 네 엄마 다 할터주고십어
翻译: 是的，妈妈，我想把一切都给你。
原文: 既然你如此顺从了我，那就先从最容易的开始证明。好狗狗，把你的照片发给妈妈看看~
翻译: 이렇게 엄마에게 순종하는 모습을 보여주니, 가장 쉬운 것부터 증명해 보자꾸나. 착한 강아지, 네 사진을 엄마에게 보내주렴~
原文: 그건 항상있죠
翻译: 那个一直都有。
原文: 啊。只需要照片就可以了。不用原件。
翻译: 아. 사진만 있으면 돼요. 원본은 필요 없어요.

将以下内容从韩文翻译成中文：
네 3일에 투표해요
2025-05-30 21:21:05,461 - DEBUG - 【构建提示词】长度: 1172 字符
2025-05-30 21:21:05,667 - DEBUG - API密钥解密成功
2025-05-30 21:21:05,667 - DEBUG - API密钥解密成功
2025-05-30 21:21:05,667 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-30 21:21:05,668 - DEBUG - 思考模式已禁用（未设置thinkingConfig参数 或预算为0）
2025-05-30 21:21:05,668 - DEBUG - 明确禁用思考模式(generationConfig.thinkingConfig.thinkingBudget=0)
2025-05-30 21:21:06,430 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "是的，3号投票。"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 759,
    "candidatesTokenCount": 6,
    "totalTokenCount": 765,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 759
      }
    ]
  },
  "modelVersion": "models/gemini-2.5-flash-preview-04-17",
  "responseId": "sqI5aKukEeTC-8YPwKG9sAE"
}

2025-05-30 21:21:06,431 - DEBUG - 【API响应JSON对象】模型: gemini-2.5-flash-preview-04-17, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '是的，3号投票。'}], 'role': 'model'}, 'finishReason': 'STOP', 'index': 0}], 'usageMetadata': {'promptTokenCount': 759, 'candidatesTokenCount': 6, 'totalTokenCount': 765, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 759}]}, 'modelVersion': 'models/gemini-2.5-flash-preview-04-17', 'responseId': 'sqI5aKukEeTC-8YPwKG9sAE'}
2025-05-30 21:21:06,431 - DEBUG - 【Token使用情况】模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:21:06,431 - DEBUG -   - 思考Token数: 0
2025-05-30 21:21:06,432 - DEBUG -   - 提示Token数: 759
2025-05-30 21:21:06,432 - DEBUG -   - 输出Token数: 6
2025-05-30 21:21:06,432 - DEBUG -   - 总Token数: 765
2025-05-30 21:21:06,433 - DEBUG - pycld2 检测结果: is_reliable=False, details=(('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:21:06,433 - DEBUG - 基于特征补充候选: zh (score: 0.3125)
2025-05-30 21:21:06,433 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.312)]
2025-05-30 21:21:06,434 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-30 21:21:06,434 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-30 21:21:06,434 - INFO - 检测到目标语言文本，识别为: zh (用于反向翻译)
2025-05-30 21:21:06,434 - INFO - API翻译成功。模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:21:06,940 - DEBUG - 输入框内容已替换为: 是的，3号投票。
2025-05-30 21:21:06,940 - INFO - 【翻译结果】
是的，3号投票。
2025-05-30 21:21:06,941 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-30 21:21:06,941 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-30 21:21:06,942 - INFO - 翻译完成，结果已替换输入框内容
2025-05-30 21:21:06,943 - INFO - 已立即保存 2 条缓存记录
2025-05-30 21:21:06,948 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-30 21:21:12,649 - INFO - 检测到三次空格，触发翻译
2025-05-30 21:21:12,651 - INFO - 【原文】
啊。明白了。现在到家了把？
2025-05-30 21:21:12,652 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 97, 1830.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:21:12,653 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('zh', 0.91)]
2025-05-30 21:21:12,653 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: zh
2025-05-30 21:21:12,653 - DEBUG - 【缓存更新】保存语言检测结果: zh
2025-05-30 21:21:12,653 - INFO - 检测到原文语言: zh
2025-05-30 21:21:12,653 - INFO - 执行正向翻译为: ko
2025-05-30 21:21:12,653 - INFO - 当前模型温度: 0.1, Top-P: 0.85
2025-05-30 21:21:12,654 - DEBUG - 检测到语言: zh, 输入语言: 中文(zh), 输出语言: 韩文(ko)
2025-05-30 21:21:12,654 - DEBUG - 使用语气词 - 输入: [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜], 输出: [ㅋ|ㅎ|아|네|헤|ㅜ]
2025-05-30 21:21:12,654 - DEBUG - 思考预算为0，思考模式已禁用
2025-05-30 21:21:12,654 - INFO - 模式 2 当前上下文数量: 8（最大: 8）
2025-05-30 21:21:12,654 - DEBUG - 发给大模型的完整提示词:

你是一个专业的多语言翻译专家，精通多种语言的互译。
翻译规则如下：
- 若用户指定输入为 中文 和输出为 韩文 ，将内容从 中文 翻译成 韩文 ，使用敬语
- 若未指定语言或输入既非 中文 也非 韩文 ，则翻译为 中文 。
- 若无法准确检测输入语言，返回"语言检测错误，请明确指定输入语言"。
- 严格要求：
  - 输出为纯 韩文，调整语气和风格，考虑文化内涵和地区差异。不得包含 中文 或其他语言的字符。
- 语气词翻译规则：
    * 仅当原文中明确包含 [哈哈|嘿嘿|呵呵|哦|嘛|呀|哟|哦|呜] 中的语气助词时，才将其翻译为 韩文 中等效的 [ㅋ|ㅎ|아|네|헤|ㅜ] 语气助词
    * 若原文中不包含语气助词，请不要在译文中添加额外的语气助词
    * 保持原文的语气强度和情感色彩，不夸大也不减弱
  - 将多行输入翻译为自然、连贯的单段文本，除非需要保留多行来达到精准的翻译。
  - 根据 韩文 的语法和习惯，灵活调整标点符号，保留原文语气功能。
  - 保留原始数字单位，翻译为 韩文 的自然表达，并调整格式。
  - 保持原文语义完整，遵循翻译理论中的三个核心"信达雅"，不增删核心内容。
  - 根据用户输入或对话历史中的上下文，确保翻译一致。
- 请注意！输出仅为翻译结果，不含任何说明，除非返回错误提示！


以下是最近的上下文，请参考上下文翻译以保持一致性：
原文: 是的。我昨天让你准备的资料准备好了没有？身份证或者驾驶证或者护照。
翻译: 네. 제가 어제 준비해 달라고 했던 자료는 준비되셨나요? 신분증이나 운전면허증, 아니면 여권이요.
原文: ㅎㅎㅎㅎ 전 이제 퇴근해서 ㅎㅎ
翻译: 哈哈哈哈，我刚下班，哈哈。
原文: 啊。真辛苦呢。每天你准备一下身份证或者护照或者驾驶证吧，我带你注册交易所。
翻译: 아. 정말 수고 많으셨네요. 매일 신분증이나 여권, 아니면 운전면허증을 준비해 주세요. 제가 거래소 등록을 도와드릴게요.
原文: 네 엄마 다 할터주고십어
翻译: 是的，妈妈，我想把一切都给你。
原文: 既然你如此顺从了我，那就先从最容易的开始证明。好狗狗，把你的照片发给妈妈看看~
翻译: 이렇게 엄마에게 순종하는 모습을 보여주니, 가장 쉬운 것부터 증명해 보자꾸나. 착한 강아지, 네 사진을 엄마에게 보내주렴~
原文: 그건 항상있죠
翻译: 那个一直都有。
原文: 啊。只需要照片就可以了。不用原件。
翻译: 아. 사진만 있으면 돼요. 원본은 필요 없어요.
原文: 네 3일에 투표해요
翻译: 是的，3号投票。

将以下内容从中文翻译成韩文，使用敬语：
啊。明白了。现在到家了把？
2025-05-30 21:21:12,655 - DEBUG - 【构建提示词】长度: 1147 字符
2025-05-30 21:21:12,760 - DEBUG - API密钥解密成功
2025-05-30 21:21:12,761 - DEBUG - API密钥解密成功
2025-05-30 21:21:12,761 - DEBUG - 使用API密钥进行翻译请求: AIzaS...
2025-05-30 21:21:12,761 - DEBUG - 思考模式已禁用（未设置thinkingConfig参数 或预算为0）
2025-05-30 21:21:12,761 - DEBUG - 明确禁用思考模式(generationConfig.thinkingConfig.thinkingBudget=0)
2025-05-30 21:21:13,640 - DEBUG - 【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 200, 响应文本 (前1000字符): {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "아. 알겠습니다. 지금 집에 도착하셨나요?"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "index": 0
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 742,
    "candidatesTokenCount": 12,
    "totalTokenCount": 754,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 742
      }
    ]
  },
  "modelVersion": "models/gemini-2.5-flash-preview-04-17",
  "responseId": "uaI5aIP0IJqF-8YPg8zn-QE"
}

2025-05-30 21:21:13,640 - DEBUG - 【API响应JSON对象】模型: gemini-2.5-flash-preview-04-17, 响应JSON: {'candidates': [{'content': {'parts': [{'text': '아. 알겠습니다. 지금 집에 도착하셨나요?'}], 'role': 'model'}, 'finishReason': 'STOP', 'index': 0}], 'usageMetadata': {'promptTokenCount': 742, 'candidatesTokenCount': 12, 'totalTokenCount': 754, 'promptTokensDetails': [{'modality': 'TEXT', 'tokenCount': 742}]}, 'modelVersion': 'models/gemini-2.5-flash-preview-04-17', 'responseId': 'uaI5aIP0IJqF-8YPg8zn-QE'}
2025-05-30 21:21:13,641 - DEBUG - 【Token使用情况】模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:21:13,641 - DEBUG -   - 思考Token数: 0
2025-05-30 21:21:13,641 - DEBUG -   - 提示Token数: 742
2025-05-30 21:21:13,642 - DEBUG -   - 输出Token数: 12
2025-05-30 21:21:13,642 - DEBUG -   - 总Token数: 754
2025-05-30 21:21:13,643 - DEBUG - pycld2 检测结果: is_reliable=True, details=(('Korean', 'ko', 98, 3709.0), ('Unknown', 'un', 0, 0.0), ('Unknown', 'un', 0, 0.0))
2025-05-30 21:21:13,643 - DEBUG - 决策逻辑：综合评分排序 (含提示 'None'): [('ko', 0.895)]
2025-05-30 21:21:13,644 - DEBUG - 决策逻辑：无歧义或未解决歧义，选择得分最高的语言: ko
2025-05-30 21:21:13,644 - DEBUG - 【缓存更新】保存语言检测结果: ko
2025-05-30 21:21:13,644 - INFO - 检测到目标语言文本，识别为: ko (用于反向翻译)
2025-05-30 21:21:13,644 - INFO - API翻译成功。模型: gemini-2.5-flash-preview-04-17
2025-05-30 21:21:14,172 - DEBUG - 输入框内容已替换为: 아. 알겠습니다. 지금 집에 도착하셨나요?
2025-05-30 21:21:14,173 - INFO - 【翻译结果】
아. 알겠습니다. 지금 집에 도착하셨나요?
2025-05-30 21:21:14,174 - INFO - 【内存缓存更新】翻译结果已存入内存缓存
2025-05-30 21:21:14,174 - INFO - 【内存缓存更新】反向翻译结果已存入内存缓存
2025-05-30 21:21:14,175 - INFO - 翻译完成，结果已替换输入框内容
2025-05-30 21:21:14,177 - INFO - 已立即保存 2 条缓存记录
2025-05-30 21:21:14,180 - INFO - 【本地缓存更新】翻译结果已存入本地缓存
2025-05-30 21:22:35,851 - INFO - 🔄 🚀 触发翻译
2025-05-30 21:22:35,852 - INFO - 【原文】
是的。我昨天让你准备的资料准备好了没有？
2025-05-30 21:22:35,854 - INFO - 【翻译结果】
네. 제가 어제 준비해 달라고 했던 자료는 준비되셨나요?
2025-05-30 21:22:35,855 - INFO - 🔄 ✅ 结果已更新
2025-05-30 21:22:35,855 - INFO - 🔄 🚀 触发翻译
2025-05-30 21:22:35,855 - INFO - 【原文】
是的。我昨天让你准备的资料准备好了没有？
2025-05-30 21:22:35,857 - INFO - 【翻译结果】
네. 제가 어제 준비해 달라고 했던 자료는 준비되셨나요?
2025-05-30 21:22:35,857 - INFO - 🔄 ✅ 结果已更新
