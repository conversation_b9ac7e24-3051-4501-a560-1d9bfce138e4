#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志优化测试脚本

用于测试新的日志格式化器和过滤器的效果
"""

import logging
import sys
import os

# 添加当前目录到路径，以便导入主模块
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_log_messages():
    """测试各种类型的日志消息"""

    # 导入主模块以获取配置好的logger
    try:
        from 语言互译 import logger, set_log_debug_mode
        print("成功导入优化后的日志系统")
    except ImportError as e:
        print(f"导入失败: {e}")
        return

    print("\n=== 测试简化模式日志 ===\n")
    set_log_debug_mode(False)  # 设置为简化模式

    # 测试翻译相关日志
    logger.info("检测到三次空格，触发翻译")
    logger.info("【原文】\n你好，很高兴认识你。")
    logger.info("检测到原文语言: zh")
    logger.info("执行正向翻译为: en")
    logger.info("【翻译结果】\nHello, it's nice to meet you.")
    logger.info("翻译完成，结果已替换输入框内容")

    # 测试缓存相关日志
    logger.info("【缓存命中】翻译结果来自缓存")
    logger.info("【缓存更新】保存语言检测结果: zh")
    logger.info("【内存缓存更新】翻译结果已存入内存缓存")
    logger.info("【本地缓存更新】翻译结果已存入本地缓存")

    # 测试API相关日志
    logger.info("API翻译成功。模型: gemini-2.5-flash-preview-04-17")
    logger.info("使用API密钥进行翻译请求: AIzaS...")
    logger.info("API密钥解密成功")

    # 测试应该被过滤的详细信息
    logger.debug("【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 200")
    logger.debug("【构建提示词】长度: 619 字符")
    logger.debug("pycld2 检测结果: is_reliable=True")
    logger.debug("决策逻辑：综合评分排序")
    logger.debug("当前模型温度: 0.1, Top-P: 0.85")
    logger.debug("Token使用情况：提示Token数: 400")

    print("\n=== 测试调试模式日志 ===\n")
    set_log_debug_mode(True)  # 设置为调试模式

    # 重新测试相同的日志消息
    logger.info("检测到三次空格，触发翻译")
    logger.info("【原文】\n你好，很高兴认识你。这是一个测试文本，用来验证调试模式下的日志显示效果。")
    logger.info("检测到原文语言: zh")
    logger.info("执行正向翻译为: en")
    logger.info("【翻译结果】\nHello, nice to meet you. This is a test text to verify the log display effect in debug mode.")
    logger.info("翻译完成，结果已替换输入框内容")

    # 测试调试模式下的详细信息
    logger.info("发给大模型的完整提示词: 这是一个测试提示词...")
    logger.debug("【API响应原始文本】模型: gemini-2.5-flash-preview-04-17, 状态码: 200")
    logger.debug("当前模型温度: 0.1, Top-P: 0.85")
    logger.debug("Token使用情况：提示Token数: 400, 输出Token数: 15")
    logger.debug("pycld2 检测结果: is_reliable=True, details=(('Chinese', 'zh', 96, 2048.0))")

    # 测试错误和警告
    logger.warning("API服务可能不健康: 连接超时")
    logger.error("翻译请求失败: 网络连接错误")

    # 测试配置相关日志
    logger.info("已成功生成默认配置文件: config.yaml")
    logger.info("语言模式配置已加载。")

    # 测试网络相关日志
    logger.info("网络检查成功，连接正常")
    logger.warning("网络检查失败 连接到 8.8.8.8:53")

    print("\n=== 日志测试完成 ===")
    print("对比观察：")
    print("1. 简化模式：只显示关键信息，原文和译文被截断")
    print("2. 调试模式：显示完整的原文和译文，保留技术细节")
    print("3. 日志是否有颜色和图标")
    print("4. 时间格式是否简化为 HH:MM:SS")
    print("5. 在设置菜单选项13中可以切换这两种模式")

if __name__ == "__main__":
    test_log_messages()
